<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-formats</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-sql-parquet</artifactId>
	<name>Flink : Formats : SQL Parquet</name>

	<packaging>jar</packaging>

	<properties>
		<japicmp.skip>true</japicmp.skip>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-parquet</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.parquet</groupId>
			<artifactId>parquet-avro</artifactId>
			<version>${flink.format.parquet.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
			<exclusions>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>it.unimi.dsi</groupId>
					<artifactId>fastutil</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes>
									<include>org.apache.flink:flink-parquet</include>
									<include>org.apache.parquet:parquet-avro</include>
									<include>org.apache.parquet:parquet-hadoop</include>
									<include>org.apache.parquet:parquet-format</include>
									<include>org.apache.parquet:parquet-column</include>
									<include>org.apache.parquet:parquet-common</include>
									<include>org.apache.parquet:parquet-encoding</include>
									<include>org.apache.parquet:parquet-format-structures</include>
									<include>org.apache.parquet:parquet-jackson</include>
									<include>org.codehaus.jackson:jackson-core-asl</include>
									<include>org.codehaus.jackson:jackson-mapper-asl</include>
									<include>commons-pool:commons-pool</include>
									<include>commons-codec:commons-codec</include>
								</includes>
							</artifactSet>
							<relocations>
								<relocation>
									<pattern>org.apache.avro</pattern>
									<shadedPattern>org.apache.flink.avro.shaded.org.apache.avro</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- skip dependency convergence due to Hadoop dependency -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>dependency-convergence</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<skip>true</skip>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
