<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-formats</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-sql-avro</artifactId>
	<name>Flink : Formats : SQL Avro</name>

	<packaging>jar</packaging>

	<properties>
		<japicmp.skip>true</japicmp.skip>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-avro</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes>
									<include>org.apache.flink:flink-avro</include>
									<include>org.apache.avro:avro</include>
									<include>com.fasterxml.jackson.core:jackson-core</include>
									<include>com.fasterxml.jackson.core:jackson-databind</include>
									<include>com.fasterxml.jackson.core:jackson-annotations</include>
									<include>org.apache.commons:commons-compress</include>
								</includes>
							</artifactSet>
							<relocations>
								<relocation>
									<pattern>com.fasterxml.jackson</pattern>
									<shadedPattern>org.apache.flink.avro.shaded.com.fasterxml.jackson</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.commons.compress</pattern>
									<shadedPattern>org.apache.flink.avro.shaded.org.apache.commons.compress</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.avro</pattern>
									<shadedPattern>org.apache.flink.avro.shaded.org.apache.avro</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
