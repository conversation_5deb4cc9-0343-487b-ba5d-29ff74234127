<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-formats</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-avro</artifactId>
	<name>Flink : Formats : Avro</name>

	<packaging>jar</packaging>

	<properties>
		<surefire.module.config><!--
			Kryo ByteBuffer
			-->--add-opens=java.base/java.nio=ALL-UNNAMED <!--
			Kryo LocalDate
			-->--add-opens=java.base/java.time=ALL-UNNAMED <!--
			Kryo
			-->--add-opens=java.base/java.lang=ALL-UNNAMED <!--
			chill ArraysAsListSerializer
			-->--add-opens=java.base/java.util=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>

		<!-- Core -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- Table ecosystem and filesystem connector -->

		<!-- Data stream projects depending on this project might not depend on flink-table-*. -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-common</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-files</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<!-- Avro -->

		<dependency>
			<groupId>org.apache.avro</groupId>
			<artifactId>avro</artifactId>
			<!-- managed version -->
		</dependency>

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<!-- managed version -->
			<scope>provided</scope>
			<!-- Avro records can contain JodaTime fields when using logical fields.
				In order to handle them, we need to add an optional dependency.
				Users with those Avro records need to add this dependency themselves. -->
			<optional>true</optional>
		</dependency>

		<!-- Tests -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-datagen</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<!-- ArchUnit test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-architecture-tests-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-common</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-planner_${scala.binary.version}</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-scala-bridge_${scala.binary.version}</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-planner_${scala.binary.version}</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<!-- Required due to UniqueBucketAssigner -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-file-sink-common</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-migration-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<executions>
					<execution>
						<id>create-test-dependency</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.formats.avro.testjar.AvroExternalJarProgram</mainClass>
								</manifest>
							</archive>
							<finalName>maven</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!--Remove the AvroExternalJarProgram code from the test-classes directory since it musn't be in the
			classpath when running the tests to actually test whether the user code class loader
			is properly used.-->
			<plugin>
				<artifactId>maven-clean-plugin</artifactId>
				<version>2.5</version><!--$NO-MVN-MAN-VER$-->
				<executions>
					<execution>
						<id>remove-avroexternalprogram</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>clean</goal>
						</goals>
						<configuration>
							<excludeDefaultDirectories>true</excludeDefaultDirectories>
							<filesets>
								<fileset>
									<directory>${project.build.testOutputDirectory}</directory>
									<includes>
										<include>**/testjar/*.class</include>
									</includes>
								</fileset>
							</filesets>
						</configuration>
					</execution>
				</executions>
				<configuration>
					<filesets>
						<fileset>
							<directory>${project.basedir}/src/test/java/org/apache/flink/formats/avro/generated</directory>
						</fileset>
					</filesets>
				</configuration>
			</plugin>

			<!-- Generate Test class from avro schema -->
			<plugin>
				<groupId>org.apache.avro</groupId>
				<artifactId>avro-maven-plugin</artifactId>
				<version>${avro.version}</version>
				<executions>
					<execution>
						<phase>generate-sources</phase>
						<goals>
							<goal>schema</goal>
						</goals>
						<configuration>
							<testSourceDirectory>${project.basedir}/src/test/resources/avro</testSourceDirectory>
							<testOutputDirectory>${project.basedir}/target/generated-test-sources/</testOutputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- Add Avro test classes to test jar in order to test AvroTypeInfo. -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<excludes>
						<exclude>META-INF/services/org.junit.jupiter.api.extension.Extension</exclude>
					</excludes>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>generate-migration-test-data</id>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-antrun-plugin</artifactId>
						<executions>
							<execution>
								<id>generate-migration-test-data</id>
								<phase>package</phase>
								<goals>
									<goal>run</goal>
								</goals>
								<configuration>
									<target>
										<condition property="optional.classes" value="--classes '${generate.classes}'"
												   else="">
											<isset property="generate.classes"/>
										</condition>
										<condition property="optional.prefixes"
												   value="--prefixes '${generate.prefixes}'" else="">
											<isset property="generate.prefixes"/>
										</condition>
										<java classname="org.apache.flink.test.migration.MigrationTestsSnapshotGenerator"
											  fork="true" failonerror="true" dir="${project.basedir}">
											<classpath refid="maven.test.classpath"/>
											<arg value="--dir"/>
											<arg line="${project.basedir}"/>
											<arg value="--version"/>
											<arg value="${generate.version}"/>
											<arg line="${optional.classes}"/>
											<arg line="${optional.prefixes}"/>
										</java>
									</target>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
</project>
