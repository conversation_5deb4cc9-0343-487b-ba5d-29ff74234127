<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-dist_${scala.binary.version}</artifactId>
	<name>Flink : Dist</name>
	<packaging>jar</packaging>

	<properties>
		<japicmp.skip>true</japicmp.skip>
	</properties>

	<dependencies>

		<!-- Flink project binaries -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-dist-scala_${scala.binary.version}</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime-web</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-java</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-core</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-container</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-statebackend-rocksdb</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-statebackend-forst</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-statebackend-changelog</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

        <dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-dstl-dfs</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
        </dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-kubernetes</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-yarn</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
			<exclusions>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-base</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- Default file system support. The Hadoop dependency			   -->
		<!--       is optional, so not being added to the dist jar         -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-hadoop-fs</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- Concrete logging framework - we add this only here (and not in the 
			root POM) to not tie the projects to one specific framework and make
			it easier for users to swap logging frameworks -->

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<!-- API bridge between log4j 1 and 2; included for convenience -->
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-1.2-api</artifactId>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-layout-template-json</artifactId>
			<scope>compile</scope>
		</dependency>

		<!-- Table dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-java-uber</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-runtime</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-planner-loader</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- SQL Client and planner with scala version goes in the /opt folder -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-planner_${scala.binary.version}</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-sql-client</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!--
			The following dependencies are packaged in 'examples/'
			The scope of these dependencies needs to be 'provided' so that
			they are not included into the 'flink-dist' uber jar.
		-->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-examples-streaming</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-examples-streaming-state-machine</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-examples-table_${scala.binary.version}</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!--
			The following dependencies are packaged in 'opt/' 
			The scope of these dependencies needs to be 'provided' so that
			they are not included into the 'flink-dist' uber jar.
		-->

		<!-- start optional Flink external resource drivers -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-external-resource-gpu</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- start optional Flink metrics reporters -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-dropwizard</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-graphite</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-influxdb</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-prometheus</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-jmx</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-statsd</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-datadog</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-slf4j</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-otel</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- end optional Flink metrics reporters -->

		<!-- start optional Flink libraries -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-cep</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-state-processor-api</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-files</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-json</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-csv</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-azure-fs-hadoop</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-s3-fs-hadoop</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-s3-fs-presto</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-oss-fs-hadoop</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-gs-fs-hadoop</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
			<exclusions>
				<!--
					Despite at provided scope, grpc artifacts can cause dependency resolving errors,
					unstably, during javadoc aggregation.
				 -->
				<exclusion>
					<groupId>io.grpc</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-queryable-state-runtime</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-python</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-netty-tcnative-dynamic</artifactId>
			<scope>provided</scope>
		</dependency>
		<!-- end optional Flink libraries -->

		<dependency>
			<!-- some components require jaxb-api on Java 11+, which is no longer bundled with the JDK -->
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
			<!-- packaged as an optional dependency that is only accessible on Java 11+ -->
			<!-- this entry exists to prevent a compile dependency from slipping through -->
			<scope>provided</scope>
		</dependency>

		<dependency>
			<!-- transitive dependency of jaxb-api; added for clarity -->
			<groupId>javax.activation</groupId>
			<artifactId>javax.activation-api</artifactId>
			<version>1.2.0</version>
			<!-- packaged as an optional dependency that is only accessible on Java 11+ -->
			<!-- this entry exists to prevent a compile dependency from slipping through -->
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
		<dependency>
			<groupId>com.google.code.findbugs</groupId>
			<artifactId>jsr305</artifactId>
			<version>1.3.9</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>
		<!-- end test dependencies -->
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.objenesis</groupId>
				<artifactId>objenesis</artifactId>
				<version>3.4</version>
				<optional>${flink.markBundledAsOptional}</optional>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<profiles>
		<profile>
			<!-- Creates/Removes the 'build-target' symlink in the root directory (only Unix systems) -->
			<id>symlink-build-target</id>
			<activation>
				<os>
					<family>unix</family>
				</os>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>exec-maven-plugin</artifactId>
						<version>1.5.0</version>
						<executions>
							<execution>
								<id>remove-build-target-link</id>
								<phase>clean</phase>
								<goals>
									<goal>exec</goal>
								</goals>
								<configuration>
									<executable>rm</executable>
									<arguments>
										<argument>-f</argument>
										<argument>${project.basedir}/../build-target</argument>
									</arguments>
								</configuration>
							</execution>
							<execution>
								<id>create-build-target-link</id>
								<phase>package</phase>
								<goals>
									<goal>exec</goal>
								</goals>
								<configuration>
									<executable>ln</executable>
									<arguments>
										<argument>-sfn</argument>
										<argument>${project.basedir}/target/flink-${project.version}-bin/flink-${project.version}</argument>
										<argument>${project.basedir}/../build-target</argument>
									</arguments>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>

	</profiles>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<artifactId>maven-assembly-plugin</artifactId>
					<configuration>
						<finalName>flink-${project.version}-bin</finalName>
						<appendAssemblyId>false</appendAssemblyId>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>dependency-convergence</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<skip>true</skip>
						</configuration>
					</execution>
					<execution>
						<id>forbid-direct-table-planner-dependencies</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<!-- We need to depend on table planner to ship it in the /opt folder -->
							<skip>true</skip>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!--plugin must appear BEFORE the shade-plugin to not mess up package order and include the non-uber JAR into the assembly-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<archive>
						<manifestEntries>
							<!-- jaxb-api is packaged as an optional dependency that is only accessible on Java 11 -->
							<Multi-Release>true</Multi-Release>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-javax-jars</id>
						<phase>process-resources</phase>
						<goals>
							<goal>copy</goal>
						</goals>
						<configuration>
							<artifactItems>
								<artifactItem>
									<groupId>javax.xml.bind</groupId>
									<artifactId>jaxb-api</artifactId>
									<version>${jaxb.api.version}</version>
									<type>jar</type>
									<overWrite>true</overWrite>
								</artifactItem>
								<artifactItem>
									<groupId>javax.activation</groupId>
									<artifactId>javax.activation-api</artifactId>
									<version>${javax.activation.api.version}</version>
									<type>jar</type>
									<overWrite>true</overWrite>
								</artifactItem>
							</artifactItems>
							<outputDirectory>${project.build.directory}/temporary/java11_exclusive</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>bundle-java11-exclusive-dependencies</id>
						<phase>process-resources</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<echo message="bundling java11-exclusive dependencies"/>
								<unzip dest="${project.build.directory}/classes/META-INF/versions/11">
									<fileset dir="${project.build.directory}/temporary/java11_exclusive">
										<include name="*"/>
									</fileset>
								</unzip>
							</target>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- Build uber jar -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-dist</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<shadedArtifactAttached>false</shadedArtifactAttached>
							<finalName>${project.artifactId}-${project.version}</finalName>
							<filters>
								<!-- Globally exclude log4j.properties from our JAR files. -->
								<filter>
									<artifact>*</artifact>
									<excludes>
										<exclude>org/apache/flink/runtime/util/bash/BashJavaUtils.class</exclude>
									</excludes>
								</filter>
							</filters>
							<artifactSet>
								<excludes>
									<!-- log4j 2 is bundled separately from the flink-dist jar -->
									<exclude>org.apache.logging.log4j:*</exclude>
								</excludes>
							</artifactSet>
						</configuration>
					</execution>
					<execution>
						<!--
						Historic artifact; shade-dist execution should be renamed.
						-->
						<id>shade-flink</id>
						<phase>none</phase>
					</execution>
					<!-- Build BashJavaUtils jar -->
					<execution>
						<id>bash-utils</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<shadedArtifactAttached>false</shadedArtifactAttached>
							<finalName>bash-java-utils</finalName>
							<filters>
								<!-- Include only the BashJavaUtils, other required classes should come from the flink-dist-->
								<filter>
									<artifact>org.apache.flink:*</artifact>
									<includes>
										<include>org/apache/flink/runtime/util/bash/BashJavaUtils.class</include>
									</includes>
								</filter>
							</filters>
							<artifactSet>
								<includes>
									<exclude>org.apache.logging.log4j:*</exclude>
									<include>org.apache.flink:*</include>
								</includes>
							</artifactSet>
							<transformers>
								<!-- Include a log4j2 configuration that always prints to stdout -->
								<transformer implementation="org.apache.maven.plugins.shade.resource.IncludeResourceTransformer">
									<resource>log4j2.properties</resource>
									<file>src/main/resources/log4j-bash-utils.properties</file>
								</transformer>
							</transformers>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<executions>
					<execution>
						<id>bin</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<descriptors>
								<descriptor>src/main/assemblies/bin.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>opt</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<descriptors>
								<descriptor>src/main/assemblies/opt.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>plugins</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<descriptors>
								<descriptor>src/main/assemblies/plugins.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>2.4</version><!--$NO-MVN-MAN-VER$-->
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>

		</plugins>
	</build>

</project>
