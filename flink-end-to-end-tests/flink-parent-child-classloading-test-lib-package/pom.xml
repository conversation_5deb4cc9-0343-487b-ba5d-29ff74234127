<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
    -->
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<!--
	This is used by the end-to-end test test_streaming_classloader.sh. It contains the library
	package that we put in the lib/ folder.
	-->

	<parent>
		<artifactId>flink-end-to-end-tests</artifactId>
		<groupId>org.apache.flink</groupId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>

	<artifactId>flink-parent-child-classloading-test-lib-package</artifactId>
	<name>Flink : E2E Tests : Parent Child classloading lib-package</name>
	<packaging>jar</packaging>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>LibPackage</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<finalName>LibPackage</finalName>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>

