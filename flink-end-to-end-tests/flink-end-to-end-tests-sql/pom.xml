<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Licensed to the Apache Software Foundation (ASF) under one
  ~  or more contributor license agreements.  See the NOTICE file
  ~  distributed with this work for additional information
  ~  regarding copyright ownership.  The ASF licenses this file
  ~  to you under the Apache License, Version 2.0 (the
  ~  "License"); you may not use this file except in compliance
  ~  with the License.  You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~  Unless required by applicable law or agreed to in writing, software
  ~  distributed under the License is distributed on an "AS IS" BASIS,
  ~  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~  See the License for the specific language governing permissions and
  ~  limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>flink-end-to-end-tests</artifactId>
        <groupId>org.apache.flink</groupId>
        <version>2.2-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>flink-end-to-end-tests-sql</artifactId>
    <name>Flink : E2E Tests : SQL</name>

    <dependencies>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-end-to-end-tests-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-runtime</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-json</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

        <!-- The following dependencies are for connector/format sql-jars that
            we copy using the maven-dependency-plugin. When extending the test
             to cover more connectors/formats, add a dependency here and an entry
            to the dependency-plugin configuration below.
            This ensures that all modules we actually need (as defined by the
             dependency-plugin configuration) are built before this module. -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-sql-client-test</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-test-utils-junit</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio</artifactId>
                </exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
            <scope>test</scope>
            <type>test-jar</type>
            <version>${flink.hadoop.version}</version><!--$NO-MVN-MAN-VER$-->
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-mapper-asl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-core-asl</artifactId>
                </exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-mapper-asl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-core-asl</artifactId>
                </exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <scope>test</scope>
            <type>test-jar</type>
            <version>${flink.hadoop.version}</version><!--$NO-MVN-MAN-VER$-->
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <!-- This dependency is no longer shipped with the JDK since Java 9.-->
                    <groupId>jdk.tools</groupId>
                    <artifactId>jdk.tools</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-mapper-asl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-core-asl</artifactId>
                </exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
            </exclusions>
        </dependency>
    </dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<!-- dependency convergence -->
				<groupId>org.codehaus.woodstox</groupId>
				<artifactId>stax2-api</artifactId>
				<version>4.2.1</version>
				<scope>test</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>store-classpath-in-target-for-tests</id>
                        <phase>package</phase>
                        <goals>
                            <goal>build-classpath</goal>
                        </goals>
                        <configuration>
                            <outputFile>${project.build.directory}/hadoop.classpath</outputFile>
                            <excludeGroupIds>org.apache.flink</excludeGroupIds>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <artifactItems>
                        <artifactItem>
                            <groupId>org.apache.flink</groupId>
                            <artifactId>flink-sql-client-test</artifactId>
                            <version>${project.version}</version>
                            <destFileName>SqlToolbox.jar</destFileName>
                            <type>jar</type>
                            <outputDirectory>${project.build.directory}/dependencies
                            </outputDirectory>
                        </artifactItem>
                    </artifactItems>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- This profile adds dependencies needed to execute the tests with Hadoop 3 -->
        <profile>
            <id>hadoop3-tests</id>
            <dependencies>
                <dependency>
                    <groupId>org.apache.hadoop</groupId>
                    <artifactId>hadoop-hdfs-client</artifactId>
                    <version>${flink.hadoop.version}</version>
                    <scope>test</scope>
					<exclusions>
						<exclusion>
							<groupId>ch.qos.reload4j</groupId>
							<artifactId>reload4j</artifactId>
						</exclusion>
						<exclusion>
							<groupId>org.slf4j</groupId>
							<artifactId>slf4j-reload4j</artifactId>
						</exclusion>
					</exclusions>
                </dependency>
            </dependencies>
            <dependencyManagement>
                <dependencies>
                    <dependency>
                        <!-- dependency convergence -->
                        <groupId>com.nimbusds</groupId>
                        <artifactId>nimbus-jose-jwt</artifactId>
                        <version>4.41.1</version>
                    </dependency>
                    <dependency>
                        <!-- dependency convergence -->
                        <groupId>com.squareup.okio</groupId>
                        <artifactId>okio</artifactId>
                        <version>1.17.2</version>
                    </dependency>
                </dependencies>
            </dependencyManagement>
        </profile>
    </profiles>
</project>
