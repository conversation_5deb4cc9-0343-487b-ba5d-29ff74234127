<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-kubernetes</artifactId>
	<name>Flink : Kubernetes</name>
	<packaging>jar</packaging>

	<properties>
		<kubernetes.client.version>6.13.4</kubernetes.client.version>
		<surefire.module.config><!--
			CommonTestUtils#setEnv
			-->--add-opens=java.base/java.util=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>

		<!-- core dependencies  -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-jackson</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>io.fabric8</groupId>
			<artifactId>kubernetes-client</artifactId>
			<version>${kubernetes.client.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- test dependencies -->
		<dependency>
			<groupId>io.fabric8</groupId>
			<artifactId>kubernetes-server-mock</artifactId>
			<version>${kubernetes.client.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes combine.children="append">
									<include>io.fabric8:*</include>

									<!-- Shade all the dependencies of kubernetes client  -->
									<include>com.fasterxml.jackson.core:jackson-core</include>
									<include>com.fasterxml.jackson.core:jackson-annotations</include>
									<include>com.fasterxml.jackson.core:jackson-databind</include>
									<include>com.fasterxml.jackson.dataformat:jackson-dataformat-yaml</include>
									<include>com.fasterxml.jackson.datatype:jackson-datatype-jsr310</include>
									<include>com.squareup.okhttp3:*</include>
									<include>com.squareup.okio:okio</include>
									<include>org.snakeyaml:*</include>
									<include>org.yaml:*</include>

									<include>META-INF/services/org.apache.flink.*</include>
								</includes>
							</artifactSet>
							<filters combine.children="append">
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>*.aut</exclude>
										<exclude>META-INF/services/*com.fasterxml*</exclude>
										<exclude>META-INF/proguard/**</exclude>
										<exclude>OSGI-INF/**</exclude>
										<exclude>schema/**</exclude>
										<exclude>*.vm</exclude>
										<exclude>*.properties</exclude>
										<exclude>*.xml</exclude>
										<exclude>META-INF/jandex.idx</exclude>
										<exclude>license.header</exclude>
									</excludes>
								</filter>
								<filter>
									<artifact>com.fasterxml.jackson*:*</artifact>
									<excludes>
										<exclude>META-INF/**/module-info.class</exclude>
										<exclude>META-INF/*LICENSE</exclude>
										<exclude>META-INF/*NOTICE</exclude>
									</excludes>
								</filter>
							</filters>
							<relocations>
								<!-- When "io.fabric8:kubernetes-client" is bumped, make sure to check JDK-specific Jackson classes,
								 	 and introduce new versions when necessary. -->
								<relocation>
									<pattern>META-INF/versions/11/com/fasterxml/jackson</pattern>
									<shadedPattern>META-INF/versions/11/org/apache/flink/kubernetes/shaded/com/fasterxml/jackson</shadedPattern>
								</relocation>
								<relocation>
									<pattern>META-INF/versions/17/com/fasterxml/jackson</pattern>
									<shadedPattern>META-INF/versions/17/org/apache/flink/kubernetes/shaded/com/fasterxml/jackson</shadedPattern>
								</relocation>
								<relocation>
									<pattern>META-INF/versions/19/com/fasterxml/jackson</pattern>
									<shadedPattern>META-INF/versions/19/org/apache/flink/kubernetes/shaded/com/fasterxml/jackson</shadedPattern>
								</relocation>
								<relocation>
									<pattern>com.fasterxml.jackson</pattern>
									<shadedPattern>org.apache.flink.kubernetes.shaded.com.fasterxml.jackson</shadedPattern>
								</relocation>
								<relocation>
									<pattern>okhttp3/internal/publicsuffix</pattern>
									<shadedPattern>META-INF</shadedPattern>
									<includes>
										<include>**/NOTICE</include>
									</includes>
								</relocation>
								<relocation>
									<pattern>okhttp3</pattern>
									<shadedPattern>org.apache.flink.kubernetes.shaded.okhttp3</shadedPattern>
								</relocation>
								<relocation>
									<pattern>okio</pattern>
									<shadedPattern>org.apache.flink.kubernetes.shaded.okio</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.snakeyaml</pattern>
									<shadedPattern>org.apache.flink.kubernetes.shaded.org.snakeyaml</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.yaml</pattern>
									<shadedPattern>org.apache.flink.kubernetes.shaded.org.yaml</shadedPattern>
								</relocation>
								<relocation>
								    <pattern>io.fabric8</pattern>
								    <shadedPattern>org.apache.flink.kubernetes.shaded.io.fabric8</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
