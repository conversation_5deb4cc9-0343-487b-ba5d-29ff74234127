/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.state.heap;

import org.apache.flink.api.common.typeutils.base.StringSerializer;
import org.apache.flink.util.TestLogger;

import org.junit.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.Assert.assertTrue;

/**
 * Stress test designed to reliably trigger the rehash-snapshot race condition that causes entries
 * to be skipped in CopyOnWriteStateMap.
 */
public class RehashSnapshotRaceStressTest extends TestLogger {

    /**
     * High-intensity stress test that should reliably trigger the race condition. This test runs
     * multiple threads continuously performing operations that trigger rehashing while other
     * threads take snapshots.
     */
    @Test(timeout = 30000) // 30 second timeout
    public void testHighIntensityRehashSnapshotRace() throws Exception {
        final int NUM_THREADS = 4;
        final int OPERATIONS_PER_THREAD = 1000;
        final int INITIAL_CAPACITY = 2; // Very small to trigger frequent rehashing

        CopyOnWriteStateMap<Integer, String, String> stateMap =
                new CopyOnWriteStateMap<Integer, String, String>(INITIAL_CAPACITY, StringSerializer.INSTANCE);

        final AtomicInteger totalRacesDetected = new AtomicInteger(0);
        final AtomicLong totalOperations = new AtomicLong(0);
        final ExecutorService executor = Executors.newFixedThreadPool(NUM_THREADS);
        final CountDownLatch startLatch = new CountDownLatch(1);
        final CountDownLatch finishLatch = new CountDownLatch(NUM_THREADS);
        // This mutex ensures snapshotting and stateMap modifications do not happen concurrently
        final Semaphore mutex = new Semaphore(1);

        // Start multiple threads that will cause frequent rehashing
        for (int threadId = 0; threadId < NUM_THREADS; threadId++) {
            final int finalThreadId = threadId;

            executor.submit(
                    () -> {
                        try {
                            startLatch.await();

                            for (int op = 0; op < OPERATIONS_PER_THREAD; op++) {
                                int key = finalThreadId * OPERATIONS_PER_THREAD + op;

                                // Add entry (triggers rehashing)
                                mutex.acquire();
                                stateMap.put(key, "namespace", "value-" + key);
                                mutex.release();
                                totalOperations.incrementAndGet();

                                // Take snapshot during potential rehashing
                                if (op % 10 == 0) { // Every 10th operation
                                    try {
                                        mutex.acquire();
                                        int expectedSize = stateMap.size();
                                        CopyOnWriteStateMap.StateMapEntry<Integer, String, String>[]
                                                snapshot = takeSnapshot(stateMap);
                                        int actualSize = countEntriesInSnapshot(snapshot);
                                        mutex.release();

                                        if (actualSize != expectedSize) {
                                            int racesDetected =
                                                    totalRacesDetected.incrementAndGet();
                                            System.err.println(
                                                    "RACE #"
                                                            + racesDetected
                                                            + " DETECTED by thread "
                                                            + finalThreadId
                                                            + ": Expected "
                                                            + expectedSize
                                                            + ", got "
                                                            + actualSize
                                                            + " (missing: "
                                                            + (expectedSize - actualSize)
                                                            + ")");
                                        }
                                    } catch (Exception e) {
                                        // Snapshot failures during race are expected
                                        totalRacesDetected.incrementAndGet();
                                    }
                                }

                                // Occasionally remove entries to create more dynamic rehashing
                                if (op % 50 == 0 && key > 10) {
                                    mutex.acquire();
                                    stateMap.remove(key - 10, "namespace");
                                    mutex.release();
                                }

                                // Force incremental rehash steps
                                if (op % 5 == 0) {
                                    try {
                                        triggerIncrementalRehash(stateMap);
                                    } catch (Exception e) {
                                        // Ignore - may fail during race
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        } finally {
                            finishLatch.countDown();
                        }
                    });
        }

        // Start all threads simultaneously
        startLatch.countDown();

        // Wait for completion
        finishLatch.await();
        executor.shutdown();

        System.out.println("Stress test completed:");
        System.out.println("- Total operations: " + totalOperations.get());
        System.out.println("- Total races detected: " + totalRacesDetected.get());
        System.out.println("- Final map size: " + stateMap.size());

        // The race condition should have been detected multiple times
        assertTrue(
                "Expected multiple race conditions to be detected in stress test. "
                        + "Detected: "
                        + totalRacesDetected.get()
                        + " (if this fails, the race might be harder to trigger than expected)",
                totalRacesDetected.get() > 0);
    }

    /**
     * Focused test that specifically targets the snapshot region assumption bug. This test creates
     * a scenario where entries are guaranteed to be placed outside the expected snapshot regions.
     */
    @Test
    public void testSnapshotRegionAssumptionBug() throws Exception {
        // Use power-of-2 sizes for predictable hash distribution
        CopyOnWriteStateMap<Integer, String, String> stateMap =
                new CopyOnWriteStateMap<Integer, String, String>(4, StringSerializer.INSTANCE); // Will rehash to 8

        // Fill to trigger rehashing
        for (int i = 1; i <= 6; i++) {
            stateMap.put(i, "ns", "value" + i);
        }

        // Ensure we're in rehashing state
        assertTrue("Map should be rehashing", isRehashing(stateMap));

        // Get current rehash state
        int rehashIndex = getRehashIndex(stateMap);
        Object[] incrementalTable = getIncrementalRehashTable(stateMap);
        int tableSize = incrementalTable.length;

        System.out.println("Rehash state: index=" + rehashIndex + ", tableSize=" + tableSize);

        // The snapshot logic assumes entries are only in these regions:
        // Region 1: incrementalTable[0, rehashIndex)
        // Region 2: incrementalTable[tableSize/2, tableSize/2 + rehashIndex)

        // Find positions that are NOT in these regions
        int region1Start = 0;
        int region1End = rehashIndex;
        int region2Start = tableSize / 2;
        int region2End = region2Start + rehashIndex;

        System.out.println(
                "Snapshot expects entries in regions: ["
                        + region1Start
                        + ","
                        + region1End
                        + ") and ["
                        + region2Start
                        + ","
                        + region2End
                        + ")");

        // Find a position outside these regions
        int targetPosition = -1;
        for (int pos = 0; pos < tableSize; pos++) {
            if (!((pos >= region1Start && pos < region1End)
                    || (pos >= region2Start && pos < region2End))) {
                targetPosition = pos;
                break;
            }
        }

        if (targetPosition >= 0) {
            System.out.println("Target position outside snapshot regions: " + targetPosition);

            // Find a key that will hash to this position
            Integer keyForTarget = findKeyForTargetPosition(targetPosition, tableSize);
            if (keyForTarget != null) {
                System.out.println(
                        "Found key " + keyForTarget + " that hashes to position " + targetPosition);

                // Add this key during rehashing
                stateMap.put(keyForTarget, "ns", "target-value");

                // Verify the entry is at the expected position
                if (incrementalTable[targetPosition] != null) {
                    System.out.println(
                            "Confirmed: Entry placed at target position " + targetPosition);

                    // Take snapshot
                    int expectedSize = stateMap.size();
                    CopyOnWriteStateMap.StateMapEntry<Integer, String, String>[] snapshot =
                            takeSnapshot(stateMap);
                    int actualSize = countEntriesInSnapshot(snapshot);

                    System.out.println(
                            "Snapshot result: expected=" + expectedSize + ", actual=" + actualSize);

                    if (actualSize < expectedSize) {
                        System.out.println("SUCCESS: Proved snapshot region assumption is wrong!");
                        System.out.println(
                                "Entry at position " + targetPosition + " was skipped by snapshot");

                        // This proves the hypothesis
                        throw new AssertionError(
                                "Snapshot skipped "
                                        + (expectedSize - actualSize)
                                        + " entries due to incorrect region assumptions. "
                                        + "Entry at position "
                                        + targetPosition
                                        + " (outside expected regions) was missed.");
                    }
                }
            }
        }

        System.out.println("Could not create scenario to prove region assumption bug in this run");
    }

    // Helper methods (same as in the main test class)

    private boolean isRehashing(CopyOnWriteStateMap<?, ?, ?> stateMap) throws Exception {
        Field incrementalRehashTableField =
                CopyOnWriteStateMap.class.getDeclaredField("incrementalRehashTable");
        incrementalRehashTableField.setAccessible(true);
        Object[] incrementalRehashTable = (Object[]) incrementalRehashTableField.get(stateMap);
        return incrementalRehashTable.length > 0;
    }

    private void triggerIncrementalRehash(CopyOnWriteStateMap<?, ?, ?> stateMap) throws Exception {
        Method incrementalRehashMethod =
                CopyOnWriteStateMap.class.getDeclaredMethod("incrementalRehash");
        incrementalRehashMethod.setAccessible(true);
        incrementalRehashMethod.invoke(stateMap);
    }

    @SuppressWarnings("unchecked")
    private CopyOnWriteStateMap.StateMapEntry<Integer, String, String>[] takeSnapshot(
            CopyOnWriteStateMap<Integer, String, String> stateMap) throws Exception {
        Method snapshotMethod = CopyOnWriteStateMap.class.getDeclaredMethod("snapshotMapArrays");
        snapshotMethod.setAccessible(true);
        return (CopyOnWriteStateMap.StateMapEntry<Integer, String, String>[])
                snapshotMethod.invoke(stateMap);
    }

    private int countEntriesInSnapshot(CopyOnWriteStateMap.StateMapEntry<?, ?, ?>[] snapshot) {
        int count = 0;
        for (CopyOnWriteStateMap.StateMapEntry<?, ?, ?> entry : snapshot) {
            while (entry != null) {
                count++;
                entry = getNextEntry(entry);
            }
        }
        return count;
    }

    private CopyOnWriteStateMap.StateMapEntry<?, ?, ?> getNextEntry(
            CopyOnWriteStateMap.StateMapEntry<?, ?, ?> entry) {
        try {
            Field nextField = entry.getClass().getDeclaredField("next");
            nextField.setAccessible(true);
            return (CopyOnWriteStateMap.StateMapEntry<?, ?, ?>) nextField.get(entry);
        } catch (Exception e) {
            return null;
        }
    }

    private int getRehashIndex(CopyOnWriteStateMap<?, ?, ?> stateMap) throws Exception {
        Field rehashIndexField = CopyOnWriteStateMap.class.getDeclaredField("rehashIndex");
        rehashIndexField.setAccessible(true);
        return (Integer) rehashIndexField.get(stateMap);
    }

    private Object[] getIncrementalRehashTable(CopyOnWriteStateMap<?, ?, ?> stateMap)
            throws Exception {
        Field incrementalRehashTableField =
                CopyOnWriteStateMap.class.getDeclaredField("incrementalRehashTable");
        incrementalRehashTableField.setAccessible(true);
        return (Object[]) incrementalRehashTableField.get(stateMap);
    }

    private Integer findKeyForTargetPosition(int targetPosition, int tableSize) {
        for (int candidate = 1000; candidate < 10000; candidate++) {
            int hash = computeCompositeHash(candidate, "ns");
            int position = hash & (tableSize - 1);
            if (position == targetPosition) {
                return candidate;
            }
        }
        return null;
    }

    private int computeCompositeHash(Object key, Object namespace) {
        int compositeHash = key.hashCode() ^ namespace.hashCode();
        // Simplified bit mixing
        compositeHash ^= compositeHash >>> 16;
        compositeHash *= 0x85ebca6b;
        compositeHash ^= compositeHash >>> 13;
        compositeHash *= 0xc2b2ae35;
        compositeHash ^= compositeHash >>> 16;
        return compositeHash;
    }
}
