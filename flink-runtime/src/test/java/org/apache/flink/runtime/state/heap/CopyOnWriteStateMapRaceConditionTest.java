/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.state.heap;

import org.apache.flink.api.common.typeutils.base.StringSerializer;
import org.apache.flink.util.TestLogger;

import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Tests to reliably trigger race conditions in CopyOnWriteStateMap between rehashing and snapshot
 * operations that cause entries to be skipped.
 */
public class CopyOnWriteStateMapRaceConditionTest extends TestLogger {

    private static final int INITIAL_CAPACITY = 4; // Small to trigger rehashing quickly
    private static final int NUM_ENTRIES = 100; // Enough to trigger multiple rehash cycles
    private static final String NAMESPACE = "namespace";

    /**
     * Test Scenario 1: Entry placed outside snapshot regions during incremental rehash This test
     * triggers the race where entries are placed at positions not covered by the snapshot logic's
     * assumptions.
     */
    @Test
    public void testSnapshotDuringIncrementalRehashSkipsEntries() throws Exception {
        CopyOnWriteStateMap<Integer, String, String> stateMap =
                new CopyOnWriteStateMap<Integer, String, String>(
                        INITIAL_CAPACITY,
                        StringSerializer.INSTANCE);

        // Fill map to trigger rehashing
        log.info("Filling map to trigger rehashing");
        for (int i = 0; i < NUM_ENTRIES; i++) {
            stateMap.put(i, NAMESPACE, "value-" + i);
        }

        final AtomicInteger entriesSkipped = new AtomicInteger(0);

        for (int round = 0; round < 50; round++) {
            log.info("Rehashing round " + round + " started");

            // Add entries to trigger rehashing
            for (int i = NUM_ENTRIES + round * 10;
                 i < NUM_ENTRIES + (round + 1) * 10;
                 i++) {
                stateMap.put(i, NAMESPACE, "value-" + i);
            }

            // Take snapshot during rehashing
            int expectedSize = stateMap.size();
            CopyOnWriteStateMap.StateMapEntry<
                    Integer, String, String>[]
                    snapshot = stateMap.snapshotMapArrays();

            int actualSnapshotSize =
                    countEntriesInSnapshot(snapshot);

            Assert.assertEquals(expectedSize, actualSnapshotSize);

            // Remove some entries
            for (int i = NUM_ENTRIES + round * 10;
                 i < NUM_ENTRIES + round * 10 + 5;
                 i++) {
                stateMap.remove(i, NAMESPACE);
            }
        }
    }

    private void triggerIncrementalRehash(CopyOnWriteStateMap<Integer, String, ?> stateMap) throws Exception {
        Method incrementalRehashMethod =
                CopyOnWriteStateMap.class.getDeclaredMethod("incrementalRehash");
        incrementalRehashMethod.setAccessible(true);
        incrementalRehashMethod.invoke(stateMap);
    }

    private int countEntriesInSnapshot(CopyOnWriteStateMap.StateMapEntry<?, ?, ?>[] snapshot) {
        int count = 0;
        for (CopyOnWriteStateMap.StateMapEntry<?, ?, ?> entry : snapshot) {
            while (entry != null) {
                count++;
                entry = getNextEntry(entry);
            }
        }
        return count;
    }

    private CopyOnWriteStateMap.StateMapEntry<?, ?, ?> getNextEntry(
            CopyOnWriteStateMap.StateMapEntry<?, ?, ?> entry) {
        try {
            Field nextField = entry.getClass().getDeclaredField("next");
            nextField.setAccessible(true);
            return (CopyOnWriteStateMap.StateMapEntry<?, ?, ?>) nextField.get(entry);
        } catch (Exception e) {
            return null;
        }
    }
}
