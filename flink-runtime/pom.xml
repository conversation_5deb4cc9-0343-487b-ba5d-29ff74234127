<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-runtime</artifactId>
	<name>Flink : Runtime</name>

	<packaging>jar</packaging>

	<properties>
		<!--
		Two properties below should be merged once support for jdk8 and jdk11 is dropped.
		The reason of extraction into a separate property with profile is that
		specifying -Djava.security.manager=allow within ${surefire.module.config} leads to
		hanging of surefire for jdk8 and jdk11
		-->
		<surefire.module.config.jdk21></surefire.module.config.jdk21>
		<surefire.module.config> ${surefire.module.config.jdk21} <!--
			JobManagerProcessUtilsTest
			-->--add-opens=java.base/java.util=ALL-UNNAMED <!--
			ConnectionUtilsTest
			-->--add-opens=java.base/java.lang=ALL-UNNAMED <!--
			-->--add-opens=java.base/java.net=ALL-UNNAMED <!--
			OperatorStateBackendTest
			-->--add-opens=java.base/java.io=ALL-UNNAMED <!--
			AsynchronousFileIOChannelTest
			-->--add-opens=java.base/java.util.concurrent=ALL-UNNAMED <!--
			-->-Djunit.platform.reflection.search.useLegacySemantics=true
		</surefire.module.config>
	</properties>

	<dependencies>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-datastream-api</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- core dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-rpc-core</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-rpc-akka-loader</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<!-- provides fallback AkkaRpcSystem loader for the IDE -->
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-rpc-akka-loader</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-queryable-state-client-java</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- The Hadoop FS support has only an optional dependency on Hadoop and
			gracefully handles absence of Hadoop classes -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-hadoop-fs</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-swagger</artifactId>
			<version>${flink.shaded.version}</version>
			<!-- Only required at compile-time; only flink-docs needs it at runtime -->
			<optional>true</optional>
		</dependency>

		<!-- optional dependency on Hadoop, so that Hadoop classes are not always pulled in -->

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<optional>true</optional>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-hdfs</artifactId>
			<optional>true</optional>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-mapreduce-client-core</artifactId>
			<optional>true</optional>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-netty</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-guava</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-jackson</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-zookeeper-3</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-datagen</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
		</dependency>

		<dependency>
			<groupId>commons-cli</groupId>
			<artifactId>commons-cli</artifactId>
		</dependency>

		<!-- See: https://groups.google.com/forum/#!msg/netty/-aAPDBNUnDg/SkGOXL2Ma2QJ -->
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
			<!-- Version is set in root POM -->
		</dependency>

		<dependency>
			<groupId>org.xerial.snappy</groupId>
			<artifactId>snappy-java</artifactId>
		</dependency>

		<!-- Async Profiler library -->
		<dependency>
			<groupId>tools.profiler</groupId>
			<artifactId>async-profiler</artifactId>
			<version>2.9</version>
		</dependency>

		<!-- Lz4 compression library -->
		<dependency>
			<groupId>org.lz4</groupId>
			<artifactId>lz4-java</artifactId>
		</dependency>

		<!-- air compression library -->
		<dependency>
			<groupId>io.airlift</groupId>
			<artifactId>aircompressor</artifactId>
			<version>0.27</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-jackson-module-jsonSchema</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-netty-tcnative-dynamic</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-test</artifactId>
			<version>${curator.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>3.7.0</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-migration-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<!-- Used only for additional logging. Optional because of unclear EPL 1.0 license compatibility. -->
		<dependency>
			<groupId>com.github.oshi</groupId>
			<artifactId>oshi-core</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-subclass</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>

		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
			</resource>
			<resource>
				<directory>src/main/resources-filtered</directory>
				<filtering>true</filtering>
			</resource>
		</resources>

		<plugins>
			<plugin>
				<!-- Description: https://github.com/git-commit-id/git-commit-id-maven-plugin
					Used to show the git ref when starting the jobManager. -->
				<groupId>pl.project13.maven</groupId>
				<artifactId>git-commit-id-plugin</artifactId>
				<version>4.0.2</version>
				<executions>
					<execution>
						<id>get-the-git-infos</id>
						<phase>validate</phase>
						<goals>
							<goal>revision</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<skipPoms>false</skipPoms>
					<failOnNoGitDirectory>false</failOnNoGitDirectory>
					<failOnUnableToExtractRepoInfo>false</failOnUnableToExtractRepoInfo>
					<gitDescribe>
						<!-- Don't generate the describe property -->
						<!-- It is useless due to the way Flink does branches and tags -->
						<skip>true</skip>
					</gitDescribe>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>dependency-convergence</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<skip>true</skip>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>

				<configuration>
					<suppressionsLocation combine.self="override">/tools/maven/suppressions-runtime.xml</suppressionsLocation>
				</configuration>
			</plugin>

			<!-- Add version to jar http://stackoverflow.com/questions/2712970/how-to-get-maven-artifact-version-at-runtime
				-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<inherited>true</inherited>
				<configuration>
					<archive>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
							<addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
						</manifest>
					</archive>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
						<configuration>
							<excludes>
								<!-- test-jar is still used by JUnit4 modules -->
								<exclude>META-INF/services/org.junit.jupiter.api.extension.Extension</exclude>
							</excludes>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes>
									<include>io.airlift:aircompressor</include>
								</includes>
							</artifactSet>
							<relocations>
								<relocation>
									<pattern>io.airlift.compress</pattern>
									<shadedPattern>
										org.apache.flink.shaded.io.airlift.compress
									</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>generate-migration-test-data</id>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-antrun-plugin</artifactId>
						<executions>
							<execution>
								<id>generate-migration-test-data</id>
								<phase>package</phase>
								<goals>
									<goal>run</goal>
								</goals>
								<configuration>
									<target>
										<condition property="optional.classes" value="--classes '${generate.classes}'"
												   else="">
											<isset property="generate.classes"/>
										</condition>
										<condition property="optional.prefixes"
												   value="--prefixes '${generate.prefixes}'" else="">
											<isset property="generate.prefixes"/>
										</condition>
										<java classname="org.apache.flink.test.migration.MigrationTestsSnapshotGenerator"
											  fork="true" failonerror="true" dir="${project.basedir}">
											<classpath refid="maven.test.classpath"/>
											<arg value="--dir"/>
											<arg line="${project.basedir}"/>
											<arg value="--version"/>
											<arg value="${generate.version}"/>
											<arg line="${optional.classes}"/>
											<arg line="${optional.prefixes}"/>
										</java>
									</target>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>java21</id>
			<activation>
				<jdk>[21,)</jdk>
			</activation>

			<properties>
				<surefire.module.config.jdk21> <!--
					required by AbstractFineGrainedSlotManagerITCase-->
					-Djava.security.manager=allow</surefire.module.config.jdk21>
			</properties>
		</profile>
	</profiles>
</project>
