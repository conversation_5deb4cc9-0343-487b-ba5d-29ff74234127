<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-filesystems</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-s3-fs-presto</artifactId>
	<name>Flink : FileSystems : S3 FS Presto</name>

	<packaging>jar</packaging>

	<properties>
		<presto.version>0.272</presto.version>
		<surefire.module.config> <!--
			S5CmdOnPrestoS3FileSystemITCase uses ArraysAsListSerializer indirectly
			-->--add-opens=java.base/java.util=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>

		<!-- Flink's file system abstraction (compiled against, not bundled) -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- ArchUnit test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-architecture-tests-test</artifactId>
			<scope>test</scope>
		</dependency>
		
		<!-- S3 base (bundled) -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-s3-fs-base</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- for the HAJobRunOnPrestoS3FileSystemITCase -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-s3-fs-base</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-test</artifactId>
			<version>${curator.version}</version>
			<scope>test</scope>
			<exclusions>
				<!-- This transitive dependency causes version conflicts on the classpath. -->
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Presto's S3 file system (bundled) -->
		<dependency>
			<groupId>com.facebook.presto</groupId>
			<artifactId>presto-hive</artifactId>
			<version>${presto.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
			<exclusions>
				<!-- Exclude AWS dependencies which we provide ourselves (and in more recent versions). -->
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-s3</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-sts</artifactId>
				</exclusion>

				<!-- Unfortunately, presto-hive has a lot of dependencies, many of which we don't need.
				     To try and keep our footprint small, we're excluding dependencies on a best-effort
				     basis here.
				     The best way to do this is looking at
				         https://github.com/prestodb/presto/tree/${presto.version}/presto-hive/src/main/java/com/facebook/presto/hive/s3
				     and checking which dependencies are actually used. When there is doubt, it is
				     probably easier to just bundle it. -->

				<!-- AWS -->
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-glue</artifactId>
				</exclusion>

				<!-- Presto -->
				<exclusion>
					<groupId>com.facebook.presto.hive</groupId>
					<artifactId>hive-apache</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.presto</groupId>
					<artifactId>presto-spi</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.presto</groupId>
					<artifactId>presto-plugin-toolkit</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.presto</groupId>
					<artifactId>presto-orc</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.presto</groupId>
					<artifactId>presto-rcfile</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.presto</groupId>
					<artifactId>presto-parquet</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.presto</groupId>
					<artifactId>presto-expressions</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.presto</groupId>
					<artifactId>presto-cache</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.presto</groupId>
					<artifactId>presto-memory-context</artifactId>
				</exclusion>

				<!-- Airlift -->
				<exclusion>
					<groupId>io.airlift</groupId>
					<artifactId>aircompressor</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.airlift</groupId>
					<artifactId>json</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.airlift</groupId>
					<artifactId>bootstrap</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.airlift</groupId>
					<artifactId>concurrent</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.airlift</groupId>
					<artifactId>event</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.airlift</groupId>
					<artifactId>http-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.airlift</groupId>
					<artifactId>log-manager</artifactId>
				</exclusion>

				<!-- Other -->
				<exclusion>
					<groupId>cglib</groupId>
					<artifactId>cglib-nodep</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.drift</groupId>
					<artifactId>drift-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.facebook.hive</groupId>
					<artifactId>hive-dwrf</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.github.luben</groupId>
					<artifactId>zstd-jni</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.cloud.bigdataoss</groupId>
					<artifactId>util</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.cloud.bigdataoss</groupId>
					<artifactId>gcsio</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.cloud.bigdataoss</groupId>
					<artifactId>util-hadoop</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.cloud.bigdataoss</groupId>
					<artifactId>gcs-connector</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.errorprone</groupId>
					<artifactId>error_prone_annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.j2objc</groupId>
					<artifactId>j2objc-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.code.findbugs</groupId>
					<artifactId>annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.inject.extensions</groupId>
					<artifactId>guice-multibindings</artifactId>
				</exclusion>
				<exclusion>
					<groupId>it.unimi.dsi</groupId>
					<artifactId>fastutil</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.annotation</groupId>
					<artifactId>javax.annotation-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.inject</groupId>
					<artifactId>javax.inject</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.validation</groupId>
					<artifactId>validation-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.bval</groupId>
					<artifactId>bval-jsr</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.hudi</groupId>
					<artifactId>hudi-hadoop-mr</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.thrift</groupId>
					<artifactId>libthrift</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.checkerframework</groupId>
					<artifactId>checker-qual</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.codehaus.mojo</groupId>
					<artifactId>animal-sniffer-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.openjdk.jol</groupId>
					<artifactId>jol-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.roaringbitmap</groupId>
					<artifactId>RoaringBitmap</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.roaringbitmap</groupId>
					<artifactId>shims</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>log4j-over-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.xerial.snappy</groupId>
					<artifactId>snappy-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.facebook.presto.hadoop</groupId>
			<artifactId>hadoop-apache2</artifactId>
			<version>2.7.4-9</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<!-- Hadoop requires jaxb-api for javax.xml.bind.JAXBException -->
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
			<version>${jaxb.api.version}</version>
			<!-- packaged as an optional dependency that is only accessible on Java 11+ -->
			<scope>provided</scope>
		</dependency>

		<!-- for the behavior test suite -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-hadoop-fs</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.apache.hadoop</groupId>
				<artifactId>hadoop-common</artifactId>
				<version>${fs.hadoopshaded.version}</version>
				<optional>${flink.markBundledAsOptional}</optional>
				<exclusions>
					<exclusion>
						<groupId>jdk.tools</groupId>
						<artifactId>jdk.tools</artifactId>
					</exclusion>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<groupId>ch.qos.reload4j</groupId>
						<artifactId>reload4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-reload4j</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.hadoop</groupId>
				<artifactId>hadoop-hdfs</artifactId>
				<version>${fs.hadoopshaded.version}</version>
				<optional>${flink.markBundledAsOptional}</optional>
				<exclusions>
					<exclusion>
						<groupId>jdk.tools</groupId>
						<artifactId>jdk.tools</artifactId>
					</exclusion>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<groupId>ch.qos.reload4j</groupId>
						<artifactId>reload4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-reload4j</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<!-- Bumped for security purposes -->
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>1.9.4</version>
				<optional>${flink.markBundledAsOptional}</optional>
			</dependency>
		</dependencies>
	</dependencyManagement>


	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>ban-openjdk.jol</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<rules>
								<bannedDependencies>
									<excludes>
										<exclude>org.openjdk.jol:*</exclude>
									</excludes>
									<message>Dependencies on org.openjdk.jol are not allowed due to licensing issues.</message>
								</bannedDependencies>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<archive>
						<manifestEntries>
							<!-- jaxb-api is packaged as an optional dependency that is only accessible on Java 11 -->
							<Multi-Release>true</Multi-Release>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-javax-jars</id>
						<phase>process-resources</phase>
						<goals>
							<goal>copy</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<artifactItems>
						<artifactItem>
							<groupId>javax.xml.bind</groupId>
							<artifactId>jaxb-api</artifactId>
							<version>${jaxb.api.version}</version>
							<type>jar</type>
							<overWrite>true</overWrite>
						</artifactItem>
					</artifactItems>
					<outputDirectory>${project.build.directory}/temporary</outputDirectory>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>unpack-javax-libraries</id>
						<phase>process-resources</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<echo message="unpacking javax jars"/>
								<unzip dest="${project.build.directory}/classes/META-INF/versions/11">
									<fileset dir="${project.build.directory}/temporary">
										<include name="*"/>
									</fileset>
								</unzip>
							</target>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes>
									<include>*:*</include>
								</includes>
							</artifactSet>
							<relocations>
								<!-- shade Flink's Hadoop FS adapter classes, forces plugin classloader for them -->
								<relocation>
									<pattern>org.apache.flink.runtime.fs.hdfs</pattern>
									<shadedPattern>org.apache.flink.fs.s3presto.common</shadedPattern>
								</relocation>
								<!-- shade Flink's Hadoop FS utility classes, forces plugin classloader for them -->
								<relocation>
									<pattern>org.apache.flink.runtime.util</pattern>
									<shadedPattern>org.apache.flink.fs.s3presto.common</shadedPattern>
								</relocation>
							</relocations>
							<filters>
								<filter>
									<artifact>*</artifact>
									<excludes>
										<exclude>.gitkeep</exclude>
										<exclude>mime.types</exclude>
										<exclude>mozilla/**</exclude>
										<exclude>META-INF/LICENSE.txt</exclude>
									</excludes>
								</filter>
								<filter>
									<artifact>*</artifact>
									<excludes>
										<exclude>META-INF/LICENSE.txt</exclude>
									</excludes>
								</filter>
								<filter>
									<artifact>*</artifact>
									<excludes>
										<exclude>properties.dtd</exclude>
										<exclude>PropertyList-1.0.dtd</exclude>
										<exclude>META-INF/maven/**</exclude>
										<exclude>META-INF/services/javax.xml.stream.*</exclude>
										<exclude>META-INF/LICENSE.txt</exclude>
									</excludes>
								</filter>
								<filter>
									<artifact>com.facebook.presto.hadoop:hadoop-apache2</artifact>
									<includes>
										<include>com/facebook/presto/hadoop/**</include>
									</includes>
								</filter>
								<filter>
									<artifact>com.amazonaws:aws-java-sdk-s3</artifact>
									<!-- Make sure we are using the overridden XmlResponsesSaxParser of flink-s3-fs-base.
										Filter must be removed as soon as XmlResponsesSaxParser of this module is
										dropped, for example when discontinuing support for Java 8. -->
									<excludes>
										<exclude>com/amazonaws/services/s3/model/transform/XmlResponsesSaxParser**</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
