<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-filesystems</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-azure-fs-hadoop</artifactId>
	<name>Flink : FileSystems : Azure FS Hadoop</name>

	<packaging>jar</packaging>

	<!-- need to use a release which includes this patch: https://github.com/apache/hadoop/commit/02cadbd24bf69925078d044701741e2e3fcb4b2f -->
	<properties>
		<fs.azure.sdk.version>1.16.0</fs.azure.sdk.version>
		<fs.jackson.core.version>2.9.4</fs.jackson.core.version>
		<jetty.version>9.3.24.v20180605</jetty.version>
	</properties>

	<dependencies>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- ArchUnit test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-architecture-tests-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-hadoop-fs</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-fs-hadoop-shaded</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-azure</artifactId>
			<version>${fs.hadoopshaded.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
			<exclusions>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-common</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- for Azure IT tests to check if HTTP endpoints are enabled / not -->
		<dependency>
			<groupId>com.microsoft.azure</groupId>
			<artifactId>azure</artifactId>
			<version>${fs.azure.sdk.version}</version>
			<scope>test</scope>
		</dependency>

		<!-- for the behavior test suite -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<!-- Pulled in by hadoop-azure; bumps it to the version used by 3.1.2+ for security purposes -->
				<groupId>org.eclipse.jetty</groupId>
				<artifactId>jetty-util-ajax</artifactId>
				<version>${jetty.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<!-- Relocate all Azure related classes -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes>
									<include>*:*</include>
								</includes>
							</artifactSet>
							<relocations>
								<!-- shade Flink's Hadoop FS adapter classes, forces plugin classloader for them -->
								<relocation>
									<pattern>org.apache.flink.runtime.fs.hdfs</pattern>
									<shadedPattern>org.apache.flink.fs.azure.common.hadoop</shadedPattern>
								</relocation>
								<!-- shade Flink's Hadoop FS utility classes, forces plugin classloader for them -->
								<relocation>
									<pattern>org.apache.flink.runtime.util</pattern>
									<shadedPattern>org.apache.flink.fs.azure.common</shadedPattern>
								</relocation>
							</relocations>
							<filters>
								<filter>
									<artifact>*</artifact>
									<excludes>
										<exclude>properties.dtd</exclude>
										<exclude>PropertyList-1.0.dtd</exclude>
										<exclude>mozilla/**</exclude>
										<exclude>META-INF/LICENSE.txt</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
