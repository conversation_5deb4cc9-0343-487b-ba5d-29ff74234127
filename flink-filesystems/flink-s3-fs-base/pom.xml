<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-filesystems</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-s3-fs-base</artifactId>
	<name>Flink : FileSystems : S3 FS Base</name>
	<packaging>jar</packaging>

	<properties>
		<fs.s3.aws.version>1.12.779</fs.s3.aws.version>
		<japicmp.skip>true</japicmp.skip>
		<surefire.module.config> <!--
			S5CmdOnMinioITCase uses ArraysAsListSerializer indirectly
			-->--add-opens=java.base/java.util=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>

		<!-- Flink's file system abstraction (only compiled against, not bundled) -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- ArchUnit test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-architecture-tests-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- Hadoop's file system abstraction (bundled) -->
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<version>${fs.hadoopshaded.version}</version>
			<exclusions>
				<exclusion>
					<groupId>jdk.tools</groupId>
					<artifactId>jdk.tools</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.jcraft</groupId>
					<artifactId>jsch</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jersey</groupId>
					<artifactId>jersey-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jersey</groupId>
					<artifactId>jersey-servlet</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jersey</groupId>
					<artifactId>jersey-json</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jersey</groupId>
					<artifactId>jersey-server</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.avro</groupId>
					<artifactId>avro</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.eclipse.jetty</groupId>
					<artifactId>jetty-server</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.eclipse.jetty</groupId>
					<artifactId>jetty-util</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.eclipse.jetty</groupId>
					<artifactId>jetty-servlet</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.eclipse.jetty</groupId>
					<artifactId>jetty-webapp</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>javax.servlet-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.servlet.jsp</groupId>
					<artifactId>jsp-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.kerby</groupId>
					<artifactId>kerb-simplekdc</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.curator</groupId>
					<artifactId>curator-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.curator</groupId>
					<artifactId>curator-framework</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.curator</groupId>
					<artifactId>curator-recipes</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.zookeeper</groupId>
					<artifactId>zookeeper</artifactId>
				</exclusion>
				<exclusion>
					<groupId>commons-net</groupId>
					<artifactId>commons-net</artifactId>
				</exclusion>
				<exclusion>
					<groupId>commons-cli</groupId>
					<artifactId>commons-cli</artifactId>
				</exclusion>
				<exclusion>
					<groupId>commons-codec</groupId>
					<artifactId>commons-codec</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.protobuf</groupId>
					<artifactId>protobuf-java</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.code.gson</groupId>
					<artifactId>gson</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.httpcomponents</groupId>
					<artifactId>httpclient</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-math3</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.nimbusds</groupId>
					<artifactId>nimbus-jose-jwt</artifactId>
				</exclusion>
				<exclusion>
					<groupId>net.minidev</groupId>
					<artifactId>json-smart</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- The Hadoop file system adapter classes (bundled) -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-hadoop-fs</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- AWS dependencies (bundled) -->
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-core</artifactId>
			<version>${fs.s3.aws.version}</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-s3</artifactId>
			<version>${fs.s3.aws.version}</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-kms</artifactId>
			<version>${fs.s3.aws.version}</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-dynamodb</artifactId>
			<version>${fs.s3.aws.version}</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-sts</artifactId>
			<version>${fs.s3.aws.version}</version>
		</dependency>

		<!-- Hadoop's s3 support classes (bundled) -->
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-aws</artifactId>
			<version>${fs.hadoopshaded.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-bundle</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- flink-runtime and curator dependencies are needed for the HAJobRunOnMinioS3StoreITCase -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-test</artifactId>
			<version>${curator.version}</version>
			<scope>test</scope>
			<exclusions>
				<!-- This transitive dependency causes version conflicts on the classpath. -->
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>

			<!-- this is merely an intermediate build artifact and should not be -->
			<!-- deployed to maven central                                       -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
