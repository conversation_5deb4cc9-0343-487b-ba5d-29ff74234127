<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-filesystems</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-s3-fs-hadoop</artifactId>
	<name>Flink : FileSystems : S3 FS Hadoop</name>

	<packaging>jar</packaging>

	<properties>
		<surefire.module.config> <!--
			S5CmdOnHadoopS3FileSystemITCase uses ArraysAsListSerializer indirectly
			-->--add-opens=java.base/java.util=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- Override the flink-parent dependencyManagement definition for hadoop-common to ensure
				${fs.hadoopshaded.version} is used for this file system -->
			<dependency>
				<groupId>org.apache.hadoop</groupId>
				<artifactId>hadoop-common</artifactId>
				<version>${fs.hadoopshaded.version}</version>
				<exclusions>
					<exclusion>
						<groupId>ch.qos.reload4j</groupId>
						<artifactId>reload4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-reload4j</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<!-- Bumped for security purposes -->
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>1.9.4</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<dependencies>

		<!-- Flink's file system abstraction (compiled against, not bundled) -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- ArchUnit test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-architecture-tests-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- S3 base -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-s3-fs-base</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<!-- Hadoop requires jaxb-api for javax.xml.bind.JAXBException -->
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
			<version>${jaxb.api.version}</version>
			<!-- packaged as an optional dependency that is only accessible on Java 11+ -->
			<scope>provided</scope>
		</dependency>

		<!-- for the behavior test suite -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-hadoop-fs</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<!-- for the HAJobRunOnHadoopS3FileSystemITCase -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-s3-fs-base</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-test</artifactId>
			<version>${curator.version}</version>
			<scope>test</scope>
			<exclusions>
				<!-- This transitive dependency causes version conflicts on the classpath. -->
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<archive>
						<manifestEntries>
							<!-- jaxb-api is packaged as an optional dependency that is only accessible on Java 11 -->
							<Multi-Release>true</Multi-Release>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-javax-jars</id>
						<phase>process-resources</phase>
						<goals>
							<goal>copy</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<artifactItems>
						<artifactItem>
							<groupId>javax.xml.bind</groupId>
							<artifactId>jaxb-api</artifactId>
							<version>${jaxb.api.version}</version>
							<type>jar</type>
							<overWrite>true</overWrite>
						</artifactItem>
					</artifactItems>
					<outputDirectory>${project.build.directory}/temporary</outputDirectory>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>unpack-javax-libraries</id>
						<phase>process-resources</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<echo message="unpacking javax jars"/>
								<unzip dest="${project.build.directory}/classes/META-INF/versions/11">
									<fileset dir="${project.build.directory}/temporary">
										<include name="*"/>
									</fileset>
								</unzip>
							</target>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes>
									<include>*:*</include>
								</includes>
							</artifactSet>
							<relocations>
								<!-- shade Flink's Hadoop FS adapter classes, forces plugin classloader for them -->
								<relocation>
									<pattern>org.apache.flink.runtime.fs.hdfs</pattern>
									<shadedPattern>org.apache.flink.fs.s3hadoop.common</shadedPattern>
								</relocation>
								<!-- shade Flink's Hadoop FS utility classes, forces plugin classloader for them -->
								<relocation>
									<pattern>org.apache.flink.runtime.util</pattern>
									<shadedPattern>org.apache.flink.fs.s3hadoop.common</shadedPattern>
								</relocation>
							</relocations>
							<filters>
								<filter>
									<artifact>*</artifact>
									<excludes>
										<exclude>.gitkeep</exclude>
										<exclude>mime.types</exclude>
										<exclude>mozilla/**</exclude>
										<exclude>META-INF/maven/**</exclude>
										<exclude>META-INF/LICENSE.txt</exclude>
									</excludes>
								</filter>
								<filter>
									<artifact>*</artifact>
									<excludes>
										<exclude>properties.dtd</exclude>
										<exclude>PropertyList-1.0.dtd</exclude>
										<exclude>META-INF/services/javax.xml.stream.*</exclude>
										<exclude>META-INF/LICENSE.txt</exclude>
									</excludes>
								</filter>
								<filter>
									<artifact>com.amazonaws:aws-java-sdk-s3</artifact>
									<!-- Make sure we are using the overridden XmlResponsesSaxParser of flink-s3-fs-base.
										Filter must be removed as soon as XmlResponsesSaxParser of this module is
										dropped, for example when discontinuing support for Java 8. -->
									<excludes>
										<exclude>com/amazonaws/services/s3/model/transform/XmlResponsesSaxParser**</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
