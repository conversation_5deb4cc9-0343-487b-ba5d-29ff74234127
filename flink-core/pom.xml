<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-core</artifactId>
	<name>Flink : Core</name>

	<packaging>jar</packaging>

	<properties>
		<!--
		Two properties below should be merged once support for jdk8 and jdk11 is dropped.
		The reason of extraction into a separate property with profile is that
		specifying -Djava.security.manager=allow within ${surefire.module.config} leads to
		hanging of surefire for jdk8 and jdk11
		-->
		<surefire.module.config.jdk21></surefire.module.config.jdk21>
		<surefire.module.config> ${surefire.module.config.jdk21} <!--
			required by JmxServer
			-->--add-exports=java.rmi/sun.rmi.registry=ALL-UNNAMED <!--
			PluginConfigTest (CommonTestUtils#setEnv)
			-->--add-opens=java.base/java.util=ALL-UNNAMED <!--
			ExceptionUtilsTest
			-->--add-opens=java.base/java.lang=ALL-UNNAMED <!--
			StateDescriptorTest (Kryo serialization of File)
			-->--add-opens=java.base/java.io=ALL-UNNAMED <!--
			SerializersTest (Kryo serialization of Nested1->Path->URI)
			-->--add-opens=java.base/java.net=ALL-UNNAMED <!--
			InitOutputPathTest
			-->--add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED <!--
			-->--add-opens=java.base/java.lang.reflect=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core-api</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-annotations</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- ArchUnit test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-architecture-tests-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-asm-9</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-jackson</artifactId>
		</dependency>

		<!-- standard utilities -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<!-- managed version -->
		</dependency>

		<!-- YAML parser utilities -->
		<dependency>
			<groupId>org.snakeyaml</groupId>
			<artifactId>snakeyaml-engine</artifactId>
			<version>2.6</version>
		</dependency>

		<!-- standard utilities -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<!-- managed version -->
		</dependency>

		<!-- for the fallback generic serializer -->
		<dependency>
			<groupId>com.esotericsoftware</groupId>
			<artifactId>kryo</artifactId>
			<!-- managed version -->
		</dependency>

		<!-- The common collections are needed for some hash tables used in the collection execution -->
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<!-- managed version -->
		</dependency>

		<!-- Commons compression, for additional decompressors -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<!-- managed version -->
		</dependency>
		<dependency>
			<groupId>com.github.luben</groupId>
			<artifactId>zstd-jni</artifactId>
			<version>1.4.9-1</version>
			<scope>test</scope>
		</dependency>


		<!-- Ratelimiting dependencies -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-guava</artifactId>
		</dependency>

		<!-- ================== test dependencies ================== -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>

		<!-- Joda, jackson, and lombok are used to test that serialization and type extraction
			work with types from those libraries -->

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.joda</groupId>
			<artifactId>joda-convert</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.30</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-migration-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>

				<configuration>
					<suppressionsLocation combine.self="override">/tools/maven/suppressions-core.xml</suppressionsLocation>
				</configuration>
			</plugin>

			<!-- publish some test base classes -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>

	</build>

	<profiles>
		<profile>
			<id>generate-migration-test-data</id>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-antrun-plugin</artifactId>
						<executions>
							<execution>
								<id>generate-migration-test-data</id>
								<phase>package</phase>
								<goals>
									<goal>run</goal>
								</goals>
								<configuration>
									<target>
										<condition property="optional.classes" value="--classes '${generate.classes}'"
												   else="">
											<isset property="generate.classes"/>
										</condition>
										<condition property="optional.prefixes"
												   value="--prefixes '${generate.prefixes}'" else="">
											<isset property="generate.prefixes"/>
										</condition>
										<java classname="org.apache.flink.test.migration.MigrationTestsSnapshotGenerator"
											  fork="true" failonerror="true" dir="${project.basedir}">
											<classpath refid="maven.test.classpath"/>
											<arg value="--dir"/>
											<arg line="${project.basedir}"/>
											<arg value="--version"/>
											<arg value="${generate.version}"/>
											<arg line="${optional.classes}"/>
											<arg line="${optional.prefixes}"/>
										</java>
									</target>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>java21</id>
			<activation>
				<jdk>[21,)</jdk>
			</activation>

			<properties>
				<surefire.module.config.jdk21> <!--
				    required by FlinkSecurityManagerTest-->
					-Djava.security.manager=allow</surefire.module.config.jdk21>
			</properties>
		</profile>
	</profiles>
</project>
