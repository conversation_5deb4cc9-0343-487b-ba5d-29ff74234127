<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-clients</artifactId>
	<name>Flink : Clients</name>

	<packaging>jar</packaging>

	<properties>
		<surefire.module.config><!--
			chill ArraysAsListSerializer
			-->--add-opens=java.base/java.util=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>

		<!-- core dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>commons-cli</groupId>
			<artifactId>commons-cli</artifactId>
		</dependency>

		<!-- test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-java</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-datastream</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<classifier>job-jar</classifier>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<classifier>job-lib-jar</classifier>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<classifier>additional-artifact-jar</classifier>
		</dependency>
	</dependencies>

	<!-- More information on this:
		http://stackoverflow.com/questions/1401857/using-maven-to-build-separate-jar-files-for-unit-testing-a-custom-class-loader
	 -->
	<build>
		<plugins>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.4</version><!--$NO-MVN-MAN-VER$-->
				<executions>
					<execution>
						<id>create-test-dependency</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.client.testjar.TestJob</mainClass>
								</manifest>
							</archive>
							<finalName>maven</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<!-- Copy user jars into target/ directory -->
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>pre-integration-test</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<stripVersion>true</stripVersion>
							<includeArtifactIds>flink-clients-test-utils</includeArtifactIds>
							<outputDirectory>${project.build.directory}</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<!-- Exclude user jars From classpath -->
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<classpathDependencyExcludes>
						<classpathDependencyExclude>*:flink-clients-test-utils:*</classpathDependencyExclude>
					</classpathDependencyExcludes>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
