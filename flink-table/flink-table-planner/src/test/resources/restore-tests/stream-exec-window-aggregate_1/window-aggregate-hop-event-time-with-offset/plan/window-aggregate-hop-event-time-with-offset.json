{"flinkVersion": "1.19", "nodes": [{"id": 91, "type": "stream-exec-table-source-scan_1", "scanTableSource": {"table": {"identifier": "`default_catalog`.`default_database`.`window_source_t`", "resolvedTable": {"schema": {"columns": [{"name": "ts", "dataType": "VARCHAR(2147483647)"}, {"name": "a_int", "dataType": "INT"}, {"name": "b_double", "dataType": "DOUBLE"}, {"name": "c_float", "dataType": "FLOAT"}, {"name": "d_bigdec", "dataType": "DECIMAL(10, 2)"}, {"name": "comment", "dataType": "VARCHAR(2147483647)"}, {"name": "name", "dataType": "VARCHAR(2147483647)"}, {"name": "rowtime", "kind": "COMPUTED", "expression": {"rexNode": {"kind": "CALL", "internalName": "$TO_TIMESTAMP$1", "operands": [{"kind": "INPUT_REF", "inputIndex": 0, "type": "VARCHAR(2147483647)"}], "type": "TIMESTAMP(3)"}, "serializableString": "TO_TIMESTAMP(`ts`)"}}, {"name": "proctime", "kind": "COMPUTED", "expression": {"rexNode": {"kind": "CALL", "internalName": "$PROCTIME$1", "operands": [], "type": {"type": "TIMESTAMP_WITH_LOCAL_TIME_ZONE", "nullable": false, "precision": 3, "kind": "PROCTIME"}}, "serializableString": "PROCTIME()"}}], "watermarkSpecs": [{"rowtimeAttribute": "rowtime", "expression": {"rexNode": {"kind": "CALL", "syntax": "SPECIAL", "internalName": "$-$1", "operands": [{"kind": "INPUT_REF", "inputIndex": 7, "type": "TIMESTAMP(3)"}, {"kind": "LITERAL", "value": "1000", "type": "INTERVAL SECOND(6) NOT NULL"}], "type": "TIMESTAMP(3)"}, "serializableString": "`rowtime` - INTERVAL '1' SECOND"}}]}, "partitionKeys": []}}, "abilities": [{"type": "ProjectPushDown", "projectedFields": [[1], [5], [6], [0]], "producedType": "ROW<`a_int` INT, `comment` <PERSON><PERSON><PERSON><PERSON>(2147483647), `name` <PERSON>RC<PERSON><PERSON>(2147483647), `ts` VARCHAR(2147483647)> NOT NULL"}, {"type": "ReadingMetadata", "metadataKeys": [], "producedType": "ROW<`a_int` INT, `comment` <PERSON><PERSON><PERSON><PERSON>(2147483647), `name` <PERSON>RC<PERSON><PERSON>(2147483647), `ts` VARCHAR(2147483647)> NOT NULL"}]}, "outputType": "ROW<`a_int` INT, `comment` <PERSON>RC<PERSON><PERSON>(2147483647), `name` VARCHAR(2147483647), `ts` VARCHAR(2147483647)>", "description": "TableSourceScan(table=[[default_catalog, default_database, window_source_t, project=[a_int, comment, name, ts], metadata=[]]], fields=[a_int, comment, name, ts])", "inputProperties": []}, {"id": 92, "type": "stream-exec-calc_1", "projection": [{"kind": "INPUT_REF", "inputIndex": 0, "type": "INT"}, {"kind": "INPUT_REF", "inputIndex": 1, "type": "VARCHAR(2147483647)"}, {"kind": "INPUT_REF", "inputIndex": 2, "type": "VARCHAR(2147483647)"}, {"kind": "CALL", "internalName": "$TO_TIMESTAMP$1", "operands": [{"kind": "INPUT_REF", "inputIndex": 3, "type": "VARCHAR(2147483647)"}], "type": "TIMESTAMP(3)"}], "condition": null, "inputProperties": [{"requiredDistribution": {"type": "UNKNOWN"}, "damBehavior": "PIPELINED", "priority": 0}], "outputType": "ROW<`a_int` INT, `comment` <PERSON>RC<PERSON><PERSON>(2147483647), `name` VARCHAR(2147483647), `rowtime` TIMESTAMP(3)>", "description": "Calc(select=[a_int, comment, name, TO_TIMESTAMP(ts) AS rowtime])"}, {"id": 93, "type": "stream-exec-watermark-assigner_1", "watermarkExpr": {"kind": "CALL", "syntax": "SPECIAL", "internalName": "$-$1", "operands": [{"kind": "INPUT_REF", "inputIndex": 3, "type": "TIMESTAMP(3)"}, {"kind": "LITERAL", "value": "1000", "type": "INTERVAL SECOND(6) NOT NULL"}], "type": "TIMESTAMP(3)"}, "rowtimeFieldIndex": 3, "inputProperties": [{"requiredDistribution": {"type": "UNKNOWN"}, "damBehavior": "PIPELINED", "priority": 0}], "outputType": {"type": "ROW", "fields": [{"name": "a_int", "fieldType": "INT"}, {"name": "comment", "fieldType": "VARCHAR(2147483647)"}, {"name": "name", "fieldType": "VARCHAR(2147483647)"}, {"name": "rowtime", "fieldType": {"type": "TIMESTAMP_WITHOUT_TIME_ZONE", "precision": 3, "kind": "ROWTIME"}}]}, "description": "WatermarkAssigner(rowtime=[rowtime], watermark=[(rowtime - 1000:INTERVAL SECOND)])"}, {"id": 94, "type": "stream-exec-calc_1", "projection": [{"kind": "INPUT_REF", "inputIndex": 2, "type": "VARCHAR(2147483647)"}, {"kind": "INPUT_REF", "inputIndex": 0, "type": "INT"}, {"kind": "INPUT_REF", "inputIndex": 1, "type": "VARCHAR(2147483647)"}, {"kind": "INPUT_REF", "inputIndex": 3, "type": {"type": "TIMESTAMP_WITHOUT_TIME_ZONE", "precision": 3, "kind": "ROWTIME"}}], "condition": null, "inputProperties": [{"requiredDistribution": {"type": "UNKNOWN"}, "damBehavior": "PIPELINED", "priority": 0}], "outputType": {"type": "ROW", "fields": [{"name": "name", "fieldType": "VARCHAR(2147483647)"}, {"name": "a_int", "fieldType": "INT"}, {"name": "comment", "fieldType": "VARCHAR(2147483647)"}, {"name": "rowtime", "fieldType": {"type": "TIMESTAMP_WITHOUT_TIME_ZONE", "precision": 3, "kind": "ROWTIME"}}]}, "description": "Calc(select=[name, a_int, comment, rowtime])"}, {"id": 95, "type": "stream-exec-exchange_1", "inputProperties": [{"requiredDistribution": {"type": "HASH", "keys": [0]}, "damBehavior": "PIPELINED", "priority": 0}], "outputType": {"type": "ROW", "fields": [{"name": "name", "fieldType": "VARCHAR(2147483647)"}, {"name": "a_int", "fieldType": "INT"}, {"name": "comment", "fieldType": "VARCHAR(2147483647)"}, {"name": "rowtime", "fieldType": {"type": "TIMESTAMP_WITHOUT_TIME_ZONE", "precision": 3, "kind": "ROWTIME"}}]}, "description": "Exchange(distribution=[hash[name]])"}, {"id": 96, "type": "stream-exec-window-aggregate_1", "configuration": {"table.local-time-zone": "default"}, "grouping": [0], "aggCalls": [{"name": "EXPR$3", "syntax": "FUNCTION_STAR", "internalName": "$COUNT$1", "argList": [], "filterArg": -1, "distinct": false, "approximate": false, "ignoreNulls": false, "type": "BIGINT NOT NULL"}, {"name": "EXPR$4", "internalName": "$SUM$1", "argList": [1], "filterArg": -1, "distinct": false, "approximate": false, "ignoreNulls": false, "type": "INT"}, {"name": "EXPR$5", "syntax": "FUNCTION_STAR", "internalName": "$COUNT$1", "argList": [2], "filterArg": -1, "distinct": true, "approximate": false, "ignoreNulls": false, "type": "BIGINT NOT NULL"}], "windowing": {"strategy": "TimeAttribute", "window": {"type": "HoppingWindow", "size": "PT10S", "slide": "PT5S", "offset": "PT1S"}, "timeAttributeType": {"type": "TIMESTAMP_WITHOUT_TIME_ZONE", "precision": 3, "kind": "ROWTIME"}, "timeAttributeIndex": 3, "isRowtime": true}, "namedWindowProperties": [{"name": "window_start", "property": {"kind": "WindowStart", "reference": {"name": "w$", "type": {"type": "TIMESTAMP_WITHOUT_TIME_ZONE", "precision": 3, "kind": "ROWTIME"}}}}, {"name": "window_end", "property": {"kind": "WindowEnd", "reference": {"name": "w$", "type": {"type": "TIMESTAMP_WITHOUT_TIME_ZONE", "precision": 3, "kind": "ROWTIME"}}}}], "inputProperties": [{"requiredDistribution": {"type": "UNKNOWN"}, "damBehavior": "PIPELINED", "priority": 0}], "outputType": "ROW<`name` <PERSON><PERSON><PERSON><PERSON>(2147483647), `EXPR$3` BIGINT NOT NULL, `EXPR$4` INT, `EXPR$5` BIGINT NOT NULL, `window_start` TIMESTAMP(3) NOT NULL, `window_end` TIMESTAMP(3) NOT NULL>", "description": "WindowAggregate(groupBy=[name], window=[HOP(time_col=[rowtime], size=[10 s], slide=[5 s], offset=[1 s])], select=[name, COUNT(*) AS EXPR$3, SUM(a_int) AS EXPR$4, COUNT(DISTINCT comment) AS EXPR$5, start('w$) AS window_start, end('w$) AS window_end])"}, {"id": 97, "type": "stream-exec-calc_1", "projection": [{"kind": "INPUT_REF", "inputIndex": 0, "type": "VARCHAR(2147483647)"}, {"kind": "INPUT_REF", "inputIndex": 4, "type": "TIMESTAMP(3) NOT NULL"}, {"kind": "INPUT_REF", "inputIndex": 5, "type": "TIMESTAMP(3) NOT NULL"}, {"kind": "INPUT_REF", "inputIndex": 1, "type": "BIGINT NOT NULL"}, {"kind": "INPUT_REF", "inputIndex": 2, "type": "INT"}, {"kind": "INPUT_REF", "inputIndex": 3, "type": "BIGINT NOT NULL"}], "condition": null, "inputProperties": [{"requiredDistribution": {"type": "UNKNOWN"}, "damBehavior": "PIPELINED", "priority": 0}], "outputType": "ROW<`name` <PERSON><PERSON><PERSON><PERSON>(2147483647), `window_start` TIMESTAMP(3) NOT NULL, `window_end` TIMESTAMP(3) NOT NULL, `EXPR$3` BIGINT NOT NULL, `EXPR$4` INT, `EXPR$5` BIGINT NOT NULL>", "description": "Calc(select=[name, window_start, window_end, EXPR$3, EXPR$4, EXPR$5])"}, {"id": 98, "type": "stream-exec-sink_1", "configuration": {"table.exec.sink.keyed-shuffle": "AUTO", "table.exec.sink.not-null-enforcer": "ERROR", "table.exec.sink.rowtime-inserter": "ENABLED", "table.exec.sink.type-length-enforcer": "IGNORE", "table.exec.sink.upsert-materialize": "AUTO"}, "dynamicTableSink": {"table": {"identifier": "`default_catalog`.`default_database`.`window_sink_t`", "resolvedTable": {"schema": {"columns": [{"name": "name", "dataType": "VARCHAR(2147483647)"}, {"name": "window_start", "dataType": "TIMESTAMP(3)"}, {"name": "window_end", "dataType": "TIMESTAMP(3)"}, {"name": "cnt", "dataType": "BIGINT"}, {"name": "sum_int", "dataType": "INT"}, {"name": "distinct_cnt", "dataType": "BIGINT"}], "watermarkSpecs": []}, "partitionKeys": []}}}, "inputChangelogMode": ["INSERT"], "inputProperties": [{"requiredDistribution": {"type": "UNKNOWN"}, "damBehavior": "PIPELINED", "priority": 0}], "outputType": "ROW<`name` <PERSON><PERSON><PERSON><PERSON>(2147483647), `window_start` TIMESTAMP(3) NOT NULL, `window_end` TIMESTAMP(3) NOT NULL, `EXPR$3` BIGINT NOT NULL, `EXPR$4` INT, `EXPR$5` BIGINT NOT NULL>", "description": "Sink(table=[default_catalog.default_database.window_sink_t], fields=[name, window_start, window_end, EXPR$3, EXPR$4, EXPR$5])"}], "edges": [{"source": 91, "target": 92, "shuffle": {"type": "FORWARD"}, "shuffleMode": "PIPELINED"}, {"source": 92, "target": 93, "shuffle": {"type": "FORWARD"}, "shuffleMode": "PIPELINED"}, {"source": 93, "target": 94, "shuffle": {"type": "FORWARD"}, "shuffleMode": "PIPELINED"}, {"source": 94, "target": 95, "shuffle": {"type": "FORWARD"}, "shuffleMode": "PIPELINED"}, {"source": 95, "target": 96, "shuffle": {"type": "FORWARD"}, "shuffleMode": "PIPELINED"}, {"source": 96, "target": 97, "shuffle": {"type": "FORWARD"}, "shuffleMode": "PIPELINED"}, {"source": 97, "target": 98, "shuffle": {"type": "FORWARD"}, "shuffleMode": "PIPELINED"}]}