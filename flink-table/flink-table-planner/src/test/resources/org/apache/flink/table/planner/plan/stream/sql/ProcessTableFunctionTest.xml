<?xml version="1.0" ?>
<!--
Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to you under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<Root>
  <TestCase name="testDescriptors">
    <Resource name="sql">
      <![CDATA[SELECT * FROM f(columnList1 => DESCRIPTOR(a), columnList2 => DESCRIPTOR(b, c), columnList3 => DESCRIPTOR())]]>
    </Resource>
    <Resource name="ast">
      <![CDATA[
LogicalProject(out=[$0])
+- LogicalTableFunctionScan(invocation=[f(DESCRIPTOR(_UTF-16LE'a'), DESCRIPTOR(_UTF-16LE'b', _UTF-16LE'c'), DESCRIPTOR(), DEFAULT(), DEFAULT())], rowType=[RecordType(VARCHAR(**********) out)])
]]>
    </Resource>
    <Resource name="optimized rel plan">
      <![CDATA[
ProcessTableFunction(invocation=[f(DESCRIPTOR(_UTF-16LE'a'), DESCRIPTOR(_UTF-16LE'b', _UTF-16LE'c'), DESCRIPTOR(), DEFAULT(), DEFAULT())], uid=[null], select=[out], rowType=[RecordType(VARCHAR(**********) out)])
+- Values(tuples=[[{  }]])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testDifferentPartitionKey">
    <Resource name="sql">
      <![CDATA[SELECT * FROM f(r => TABLE t PARTITION BY score, i => 1)]]>
    </Resource>
    <Resource name="ast">
      <![CDATA[
LogicalProject(score=[$0], out=[$1])
+- LogicalTableFunctionScan(invocation=[f(TABLE(#0) PARTITION BY($1), 1, DEFAULT(), DEFAULT())], rowType=[RecordType(INTEGER score, VARCHAR(**********) out)])
   +- LogicalProject(name=[$0], score=[$1])
      +- LogicalProject(name=[$0], score=[$1])
         +- LogicalValues(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
]]>
    </Resource>
    <Resource name="optimized rel plan">
      <![CDATA[
ProcessTableFunction(invocation=[f(TABLE(#0) PARTITION BY($1), 1, DEFAULT(), DEFAULT())], uid=[f], select=[score,out], rowType=[RecordType(INTEGER score, VARCHAR(**********) out)])
+- Exchange(distribution=[hash[score]])
   +- Values(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testEmptyArgs">
    <Resource name="sql">
      <![CDATA[SELECT * FROM f(uid => 'my-ptf')]]>
    </Resource>
    <Resource name="ast">
      <![CDATA[
LogicalProject(out=[$0])
+- LogicalTableFunctionScan(invocation=[f(DEFAULT(), _UTF-16LE'my-ptf')], rowType=[RecordType(VARCHAR(**********) out)])
]]>
    </Resource>
    <Resource name="optimized rel plan">
      <![CDATA[
ProcessTableFunction(invocation=[f(DEFAULT(), _UTF-16LE'my-ptf')], uid=[my-ptf], select=[out], rowType=[RecordType(VARCHAR(**********) out)])
+- Values(tuples=[[{  }]])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testOnTime">
    <Resource name="sql">
      <![CDATA[SELECT `out`, `rowtime` FROM f(r => TABLE t_watermarked, on_time => DESCRIPTOR(ts))]]>
    </Resource>
    <Resource name="ast">
      <![CDATA[
LogicalProject(out=[$0], rowtime=[$1])
+- LogicalTableFunctionScan(invocation=[f(TABLE(#0), DESCRIPTOR(_UTF-16LE'ts'), DEFAULT())], rowType=[RecordType(VARCHAR(**********) out, TIMESTAMP_LTZ(3) *ROWTIME* rowtime)])
   +- LogicalProject(name=[$0], score=[$1], ts=[$2])
      +- LogicalWatermarkAssigner(rowtime=[ts], watermark=[$2])
         +- LogicalTableScan(table=[[default_catalog, default_database, t_watermarked]])
]]>
    </Resource>
    <Resource name="optimized rel plan">
      <![CDATA[
ProcessTableFunction(invocation=[f(TABLE(#0), DESCRIPTOR(_UTF-16LE'ts'), DEFAULT())], uid=[null], select=[out,rowtime], rowType=[RecordType(VARCHAR(**********) out, TIMESTAMP_LTZ(3) *ROWTIME* rowtime)])
+- WatermarkAssigner(rowtime=[ts], watermark=[ts])
   +- TableSourceScan(table=[[default_catalog, default_database, t_watermarked]], fields=[name, score, ts])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testScalarArgsNoUid">
    <Resource name="sql">
      <![CDATA[SELECT * FROM f(i => 1, b => true)]]>
    </Resource>
    <Resource name="ast">
      <![CDATA[
LogicalProject(out=[$0])
+- LogicalTableFunctionScan(invocation=[f(1, true, DEFAULT(), DEFAULT())], rowType=[RecordType(VARCHAR(**********) out)])
]]>
    </Resource>
    <Resource name="optimized rel plan">
      <![CDATA[
ProcessTableFunction(invocation=[f(1, true, DEFAULT(), DEFAULT())], uid=[null], select=[out], rowType=[RecordType(VARCHAR(**********) out)])
+- Values(tuples=[[{  }]])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testUidPipelineSplitIntoTwoFunctions">
    <Resource name="ast">
      <![CDATA[
LogicalSink(table=[default_catalog.default_database.t_keyed_sink], fields=[name, out])
+- LogicalProject(name=[$0], out=[$1])
   +- LogicalTableFunctionScan(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), _UTF-16LE'a')], rowType=[RecordType(VARCHAR(5) name, VARCHAR(**********) out)])
      +- LogicalProject(name=[$0], score=[$1])
         +- LogicalProject(name=[$0], score=[$1])
            +- LogicalValues(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])

LogicalSink(table=[default_catalog.default_database.t_keyed_sink], fields=[name, out])
+- LogicalProject(name=[$0], out=[$1])
   +- LogicalTableFunctionScan(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), _UTF-16LE'b')], rowType=[RecordType(VARCHAR(5) name, VARCHAR(**********) out)])
      +- LogicalProject(name=[$0], score=[$1])
         +- LogicalProject(name=[$0], score=[$1])
            +- LogicalValues(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
]]>
    </Resource>
    <Resource name="optimized exec plan">
      <![CDATA[
Sink(table=[default_catalog.default_database.t_keyed_sink], fields=[name, out])
+- Union(all=[true], union=[name, out])
   :- ProcessTableFunction(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), _UTF-16LE'a')], uid=[a], select=[name,out], rowType=[RecordType(VARCHAR(5) name, VARCHAR(**********) out)])
   :  +- Exchange(distribution=[hash[name]])(reuse_id=[1])
   :     +- Values(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
   +- ProcessTableFunction(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), _UTF-16LE'b')], uid=[b], select=[name,out], rowType=[RecordType(VARCHAR(5) name, VARCHAR(**********) out)])
      +- Reused(reference_id=[1])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testScalarArgsWithUid">
    <Resource name="sql">
      <![CDATA[SELECT * FROM f(uid => 'my-uid', i => 1, b => true)]]>
    </Resource>
    <Resource name="ast">
      <![CDATA[
LogicalProject(out=[$0])
+- LogicalTableFunctionScan(invocation=[f(1, true, DEFAULT(), _UTF-16LE'my-uid')], rowType=[RecordType(VARCHAR(**********) out)])
]]>
    </Resource>
    <Resource name="optimized rel plan">
      <![CDATA[
ProcessTableFunction(invocation=[f(1, true, DEFAULT(), _UTF-16LE'my-uid')], uid=[my-uid], select=[out], rowType=[RecordType(VARCHAR(**********) out)])
+- Values(tuples=[[{  }]])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testRowSemanticTableOptionalUid">
    <Resource name="ast">
      <![CDATA[
LogicalSink(table=[default_catalog.default_database.t_sink], fields=[out])
+- LogicalProject(out=[$0])
   +- LogicalTableFunctionScan(invocation=[f(TABLE(#0), 1, DEFAULT(), DEFAULT())], rowType=[RecordType(VARCHAR(**********) out)])
      +- LogicalProject(name=[$0], score=[$1])
         +- LogicalProject(name=[$0], score=[$1])
            +- LogicalValues(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])

LogicalSink(table=[default_catalog.default_database.t_sink], fields=[out])
+- LogicalProject(out=[$0])
   +- LogicalTableFunctionScan(invocation=[f(TABLE(#0), 42, DEFAULT(), DEFAULT())], rowType=[RecordType(VARCHAR(**********) out)])
      +- LogicalProject(name=[$0], score=[$1])
         +- LogicalProject(name=[$0], score=[$1])
            +- LogicalValues(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
]]>
    </Resource>
    <Resource name="optimized exec plan">
      <![CDATA[
Sink(table=[default_catalog.default_database.t_sink], fields=[out])
+- Union(all=[true], union=[out])
   :- ProcessTableFunction(invocation=[f(TABLE(#0), 1, DEFAULT(), DEFAULT())], uid=[null], select=[out], rowType=[RecordType(VARCHAR(**********) out)])
   :  +- Values(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])(reuse_id=[1])
   +- ProcessTableFunction(invocation=[f(TABLE(#0), 42, DEFAULT(), DEFAULT())], uid=[null], select=[out], rowType=[RecordType(VARCHAR(**********) out)])
      +- Reused(reference_id=[1])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testRowSemanticTablePassThroughColumns">
    <Resource name="sql">
      <![CDATA[SELECT * FROM f(r => TABLE t, i => 1)]]>
    </Resource>
    <Resource name="ast">
      <![CDATA[
LogicalProject(name=[$0], score=[$1], out=[$2])
+- LogicalTableFunctionScan(invocation=[f(TABLE(#0), 1, DEFAULT(), DEFAULT())], rowType=[RecordType(VARCHAR(5) name, INTEGER score, VARCHAR(**********) out)])
   +- LogicalProject(name=[$0], score=[$1])
      +- LogicalProject(name=[$0], score=[$1])
         +- LogicalValues(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
]]>
    </Resource>
    <Resource name="optimized rel plan">
      <![CDATA[
ProcessTableFunction(invocation=[f(TABLE(#0), 1, DEFAULT(), DEFAULT())], uid=[null], select=[name,score,out], rowType=[RecordType(VARCHAR(5) name, INTEGER score, VARCHAR(**********) out)])
+- Values(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testSetSemanticTablePassThroughColumns">
    <Resource name="sql">
      <![CDATA[SELECT * FROM f(r => TABLE t PARTITION BY name, i => 1)]]>
    </Resource>
    <Resource name="ast">
      <![CDATA[
LogicalProject(name=[$0], score=[$1], out=[$2])
+- LogicalTableFunctionScan(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), DEFAULT())], rowType=[RecordType(VARCHAR(5) name, INTEGER score, VARCHAR(**********) out)])
   +- LogicalProject(name=[$0], score=[$1])
      +- LogicalProject(name=[$0], score=[$1])
         +- LogicalValues(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
]]>
    </Resource>
    <Resource name="optimized rel plan">
      <![CDATA[
ProcessTableFunction(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), DEFAULT())], uid=[f], select=[name,score,out], rowType=[RecordType(VARCHAR(5) name, INTEGER score, VARCHAR(**********) out)])
+- Exchange(distribution=[hash[name]])
   +- Values(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testUidPipelineMergeIntoOneFunction">
    <Resource name="ast">
      <![CDATA[
LogicalSink(table=[default_catalog.default_database.t_keyed_sink], fields=[name, out])
+- LogicalProject(name=[$0], out=[$1])
   +- LogicalTableFunctionScan(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), _UTF-16LE'same')], rowType=[RecordType(VARCHAR(5) name, VARCHAR(**********) out)])
      +- LogicalProject(name=[$0], score=[$1])
         +- LogicalProject(name=[$0], score=[$1])
            +- LogicalValues(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])

LogicalSink(table=[default_catalog.default_database.t_keyed_sink], fields=[name, out])
+- LogicalProject(name=[$0], out=[$1])
   +- LogicalTableFunctionScan(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), _UTF-16LE'same')], rowType=[RecordType(VARCHAR(5) name, VARCHAR(**********) out)])
      +- LogicalProject(name=[$0], score=[$1])
         +- LogicalProject(name=[$0], score=[$1])
            +- LogicalValues(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
]]>
    </Resource>
    <Resource name="optimized exec plan">
      <![CDATA[
Sink(table=[default_catalog.default_database.t_keyed_sink], fields=[name, out])
+- Union(all=[true], union=[name, out])
   :- ProcessTableFunction(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), _UTF-16LE'same')], uid=[same], select=[name,out], rowType=[RecordType(VARCHAR(5) name, VARCHAR(**********) out)])(reuse_id=[1])
   :  +- Exchange(distribution=[hash[name]])
   :     +- Values(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
   +- Reused(reference_id=[1])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testUidPipelineMergeWithFanOut">
    <Resource name="ast">
      <![CDATA[
LogicalSink(table=[default_catalog.default_database.t_keyed_sink], fields=[name, out])
+- LogicalProject(name=[$0], out=[$1])
   +- LogicalFilter(condition=[=($0, _UTF-16LE'Bob')])
      +- LogicalTableFunctionScan(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), _UTF-16LE'same')], rowType=[RecordType(VARCHAR(5) name, VARCHAR(**********) out)])
         +- LogicalProject(name=[$0], score=[$1])
            +- LogicalProject(name=[$0], score=[$1])
               +- LogicalValues(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])

LogicalSink(table=[default_catalog.default_database.t_keyed_sink], fields=[name, out])
+- LogicalProject(name=[$0], out=[$1])
   +- LogicalFilter(condition=[=($0, _UTF-16LE'Alice')])
      +- LogicalTableFunctionScan(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), _UTF-16LE'same')], rowType=[RecordType(VARCHAR(5) name, VARCHAR(**********) out)])
         +- LogicalProject(name=[$0], score=[$1])
            +- LogicalProject(name=[$0], score=[$1])
               +- LogicalValues(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
]]>
    </Resource>
    <Resource name="optimized exec plan">
      <![CDATA[
Sink(table=[default_catalog.default_database.t_keyed_sink], fields=[name, out])
+- Union(all=[true], union=[name, out])
   :- Calc(select=['Bob' AS name, out], where=[(name = 'Bob')])
   :  +- ProcessTableFunction(invocation=[f(TABLE(#0) PARTITION BY($0), 1, DEFAULT(), _UTF-16LE'same')], uid=[same], select=[name,out], rowType=[RecordType(VARCHAR(5) name, VARCHAR(**********) out)])(reuse_id=[1])
   :     +- Exchange(distribution=[hash[name]])
   :        +- Values(tuples=[[{ _UTF-16LE'Bob', 12 }, { _UTF-16LE'Alice', 42 }]])
   +- Calc(select=['Alice' AS name, out], where=[(name = 'Alice')])
      +- Reused(reference_id=[1])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testUnknownScalarArg">
    <Resource name="sql">
      <![CDATA[SELECT * FROM f(i => 1, b => true, invalid => 'invalid')]]>
    </Resource>
    <Resource name="ast">
      <![CDATA[
LogicalProject(out=[$0])
+- LogicalTableFunctionScan(invocation=[f(1, true, DEFAULT(), DEFAULT())], rowType=[RecordType(VARCHAR(**********) out)])
]]>
    </Resource>
    <Resource name="optimized rel plan">
      <![CDATA[
ProcessTableFunction(invocation=[f(1, true, DEFAULT(), DEFAULT())], uid=[null], select=[out], rowType=[RecordType(VARCHAR(**********) out)])
+- Values(tuples=[[{  }]])
]]>
    </Resource>
  </TestCase>
  <TestCase name="testTypedRowSemanticTableIgnoringColumnNames">
    <Resource name="sql">
      <![CDATA[SELECT * FROM f(u => TABLE t_name_diff, i => 1)]]>
    </Resource>
    <Resource name="ast">
      <![CDATA[
LogicalProject(out=[$0])
+- LogicalTableFunctionScan(invocation=[f(TABLE(#0), 1, DEFAULT(), DEFAULT())], rowType=[RecordType(VARCHAR(**********) out)])
   +- LogicalProject(name=[$0], different=[$1])
      +- LogicalProject(name=[_UTF-16LE'Bob'], different=[12])
         +- LogicalValues(tuples=[[{ 0 }]])
]]>
    </Resource>
    <Resource name="optimized rel plan">
      <![CDATA[
ProcessTableFunction(invocation=[f(TABLE(#0), 1, DEFAULT(), DEFAULT())], uid=[null], select=[out], rowType=[RecordType(VARCHAR(**********) out)])
+- Calc(select=['Bob' AS name, 12 AS different])
   +- Values(tuples=[[{ 0 }]])
]]>
    </Resource>
  </TestCase>
</Root>
