<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-table</artifactId>
	<name>Flink : Table : </name>

	<packaging>pom</packaging>

	<modules>
		<module>flink-table-common</module>
		<module>flink-table-api-java</module>
		<module>flink-table-api-scala</module>
		<module>flink-table-api-bridge-base</module>
		<module>flink-table-api-java-bridge</module>
		<module>flink-table-api-scala-bridge</module>
		<module>flink-table-api-java-uber</module>
		<module>flink-table-planner</module>
		<module>flink-table-planner-loader</module>
		<module>flink-table-planner-loader-bundle</module>
		<module>flink-table-runtime</module>
		<module>flink-sql-gateway-api</module>
		<module>flink-sql-gateway</module>
		<module>flink-sql-client</module>
		<module>flink-sql-jdbc-driver</module>
		<module>flink-sql-jdbc-driver-bundle</module>
		<module>flink-sql-parser</module>
		<module>flink-table-code-splitter</module>
		<module>flink-table-test-utils</module>
		<module>flink-table-calcite-bridge</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<!-- Common dependency of calcite-core and flink-test-utils -->
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>
			<!-- Common dependency of calcite-core and janino -->
			<dependency>
				<groupId>org.codehaus.janino</groupId>
				<artifactId>commons-compiler</artifactId>
				<version>${janino.version}</version>
			</dependency>
			<!-- Common dependency of calcite-core and flink-table-planner-* -->
			<dependency>
				<groupId>org.codehaus.janino</groupId>
				<artifactId>janino</artifactId>
				<version>${janino.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<properties>
		<calcite.version>1.34.0</calcite.version>
		<!-- Calcite 1.34.0 depends on 3.1.8,
		at the same time minimum 3.1.x Janino version passing Flink tests without WAs is 3.1.10,
		more details are in FLINK-27995 -->
		<janino.version>3.1.10</janino.version>
		<guava.version>33.4.0-jre</guava.version>
		<quartz.version>2.3.2</quartz.version>
	</properties>
</project>
