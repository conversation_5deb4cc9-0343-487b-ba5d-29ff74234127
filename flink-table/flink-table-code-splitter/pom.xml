<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>flink-table</artifactId>
		<groupId>org.apache.flink</groupId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-table-code-splitter</artifactId>
	<name>Flink : Table : Code Splitter</name>
	<description>
		This module contains a tool to split generated Java code
		so that each method does not exceed the limit of 64KB.
	</description>

	<packaging>jar</packaging>

	<properties>
		<antlr4.version>4.7</antlr4.version>
		<!-- override parent pom -->
		<test.excludedGroups/>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.antlr</groupId>
			<artifactId>antlr4-runtime</artifactId>
			<version>${antlr4.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<!-- Java compiler -->
		<dependency>
			<groupId>org.codehaus.janino</groupId>
			<artifactId>janino</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.codehaus.janino</groupId>
			<artifactId>commons-compiler</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.antlr</groupId>
				<artifactId>antlr4-maven-plugin</artifactId>
				<version>${antlr4.version}</version>
				<executions>
					<execution>
						<id>antlr</id>
						<goals>
							<goal>antlr4</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/generated-sources/antlr4/org/apache/flink/table/codesplit</outputDirectory>
							<visitor>true</visitor>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<configuration>
							<artifactSet>
								<includes combine.children="append">
									<include>org.antlr:antlr4-runtime</include>
								</includes>
							</artifactSet>
							<relocations>
								<relocation>
									<pattern>org.antlr.v4.runtime</pattern>
									<shadedPattern>org.apache.flink.table.shaded.org.antlr.v4.runtime</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
