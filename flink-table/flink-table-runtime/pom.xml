<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-table</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-table-runtime</artifactId>
	<name>Flink : Table : Runtime</name>
	<description>
		This module contains classes that are required by a task manager for
		execution of table programs.
	</description>

	<packaging>jar</packaging>

	<properties>
		<surefire.module.config><!--
			chill ArraysAsListSerializer
			-->--add-opens=java.base/java.util=ALL-UNNAMED <!--
			kryo AtomicBoolean
			-->--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>
		<!-- Table dependencies -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-common</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-java</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-java-bridge</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-code-splitter</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- Flink dependencies -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-cep</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- Java compiler -->
		<dependency>
			<groupId>org.codehaus.janino</groupId>
			<artifactId>janino</artifactId>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
		<dependency>
			<groupId>org.codehaus.janino</groupId>
			<artifactId>commons-compiler</artifactId>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- JsonPath -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-jsonpath</artifactId>
			<version>${flink.shaded.jsonpath.version}-${flink.shaded.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- Test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-java</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-common</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-migration-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<!-- Include runtime specific dependencies, janino and json-path. Jackson is packaged by flink distribution -->
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<keepDependenciesWithProvidedScope>false</keepDependenciesWithProvidedScope>
							<artifactSet>
								<includes combine.children="append">
									<include>org.apache.flink:flink-shaded-jsonpath</include>
									<include>org.codehaus.janino:*</include>
									<include>org.apache.flink:flink-table-code-splitter</include>
								</includes>
							</artifactSet>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>generate-migration-test-data</id>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-antrun-plugin</artifactId>
						<executions>
							<execution>
								<id>generate-migration-test-data</id>
								<phase>package</phase>
								<goals>
									<goal>run</goal>
								</goals>
								<configuration>
									<target>
										<condition property="optional.classes" value="--classes '${generate.classes}'"
												   else="">
											<isset property="generate.classes"/>
										</condition>
										<condition property="optional.prefixes"
												   value="--prefixes '${generate.prefixes}'" else="">
											<isset property="generate.prefixes"/>
										</condition>
										<java classname="org.apache.flink.test.migration.MigrationTestsSnapshotGenerator"
											  fork="true" failonerror="true" dir="${project.basedir}">
											<classpath refid="maven.test.classpath"/>
											<arg value="--dir"/>
											<arg line="${project.basedir}"/>
											<arg value="--version"/>
											<arg value="${generate.version}"/>
											<arg line="${optional.classes}"/>
											<arg line="${optional.prefixes}"/>
										</java>
									</target>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
</project>
