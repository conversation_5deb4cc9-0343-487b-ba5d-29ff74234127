<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>flink-table</artifactId>
		<groupId>org.apache.flink</groupId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-sql-parser</artifactId>
	<name>Flink : Table : SQL Parser </name>

	<packaging>jar</packaging>

	<properties>
		<!-- override parent pom -->
		<test.excludedGroups/>
		<surefire.module.config><!--
			FlinkSqlParserImplTest has to override Calcite's SqlParserTest
			JUnit changed this behavior since 5.11.x, to restore it back need this flag as mentioned at
			https://junit.org/junit5/docs/current/user-guide/#extensions-supported-utilities-search-semantics
			-->-Djunit.platform.reflection.search.useLegacySemantics=true
		</surefire.module.config>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-annotations</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava.version}</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>org.checkerframework</groupId>
					<artifactId>checker-qual</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.calcite</groupId>
			<artifactId>calcite-core</artifactId>
			<!-- When updating the Calcite version, make sure to update the dependency exclusions -->
			<version>${calcite.version}</version>
			<exclusions>
				<!--
				"mvn dependency:tree" as of Calcite 1.34.0:

				[INFO] +- org.apache.calcite:calcite-core:jar:1.34.0:compile
				[INFO] |  +- org.apache.calcite.avatica:avatica-core:jar:1.23.0:compile
				[INFO] |  +- org.apiguardian:apiguardian-api:jar:1.1.2:compile
				[INFO] |  +- org.checkerframework:checker-qual:jar:3.12.0:compile
				[INFO] |  \- org.apache.commons:commons-math3:jar:3.6.1:runtime

				Dependencies that are not needed for how we use Calcite right now.
				-->
				<exclusion>
					<groupId>org.apache.calcite.avatica</groupId>
					<artifactId>avatica-metrics</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.calcite.avatica</groupId>
					<artifactId>avatica-server</artifactId>
				</exclusion>
				<exclusion>
					<groupId>commons-io</groupId>
					<artifactId>commons-io</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.protobuf</groupId>
					<artifactId>protobuf-java</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.httpcomponents</groupId>
					<artifactId>httpclient</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.httpcomponents</groupId>
					<artifactId>httpcore</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-dbcp2</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.locationtech.jts</groupId>
					<artifactId>jts-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.locationtech.jts.io</groupId>
					<artifactId>jts-io-common</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.locationtech.proj4j</groupId>
					<artifactId>proj4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.dataformat</groupId>
					<artifactId>jackson-dataformat-yaml</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.yahoo.datasketches</groupId>
					<artifactId>sketches-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>net.hydromatic</groupId>
					<artifactId>aggdesigner-algorithm</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.jayway.jsonpath</groupId>
					<artifactId>json-path</artifactId>
				</exclusion>
				<exclusion>
					<groupId>joda-time</groupId>
					<artifactId>joda-time</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.calcite</groupId>
					<artifactId>calcite-linq4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.codehaus.janino</groupId>
					<artifactId>janino</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.codehaus.janino</groupId>
					<artifactId>commons-compiler</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.code.findbugs</groupId>
					<artifactId>jsr305</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-lang3</artifactId>
				</exclusion>
				<exclusion>
					<groupId>commons-codec</groupId>
					<artifactId>commons-codec</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.uzaygezen</groupId>
					<artifactId>uzaygezen-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.errorprone</groupId>
					<artifactId>error_prone_annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.jetbrains</groupId>
					<artifactId>annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.jetbrains.kotlin</groupId>
					<artifactId>kotlin-stdlib-jdk8</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.httpcomponents.client5</groupId>
					<artifactId>httpclient5</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.httpcomponents.core5</groupId>
					<artifactId>httpcore5</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.calcite</groupId>
			<artifactId>calcite-testkit</artifactId>
			<version>${calcite.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-dbcp2</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-lang3</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-pool2</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.jetbrains.kotlin</groupId>
					<artifactId>kotlin-stdlib-jdk8</artifactId>
				</exclusion>
				<exclusion>
					<groupId>net.hydromatic</groupId>
					<artifactId>quidem</artifactId>
				</exclusion>
				<exclusion>
					<groupId>net.hydromatic</groupId>
					<artifactId>foodmart-data-hsqldb</artifactId>
				</exclusion>
				<exclusion>
					<groupId>net.hydromatic</groupId>
					<artifactId>foodmart-queries</artifactId>
				</exclusion>
				<exclusion>
					<groupId>net.hydromatic</groupId>
					<artifactId>scott-data-hsqldb</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.calcite</groupId>
			<artifactId>calcite-linq4j</artifactId>
			<version>${calcite.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<!-- Extract parser grammar template from calcite-core.jar and put
                     it under ${project.build.directory} where all freemarker templates are. -->
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>unpack-parser-template</id>
						<phase>initialize</phase>
						<goals>
							<goal>unpack</goal>
						</goals>
						<configuration>
							<artifactItems>
								<artifactItem>
									<groupId>org.apache.calcite</groupId>
									<artifactId>calcite-core</artifactId>
									<type>jar</type>
									<!-- this lets us overwrite some of the Calcite's methods -->
									<overWrite>false</overWrite>
									<outputDirectory>${project.build.directory}/</outputDirectory>
									<includes>**/Parser.jj</includes>
								</artifactItem>
							</artifactItems>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- adding fmpp code gen -->
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-fmpp-resources</id>
						<phase>initialize</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/codegen</outputDirectory>
							<resources>
								<resource>
									<directory>src/main/codegen</directory>
									<filtering>false</filtering>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin> <!-- generate sources from fmpp -->
				<groupId>org.apache.drill.tools</groupId>
				<artifactId>drill-fmpp-maven-plugin</artifactId>
				<version>1.21.2</version>
				<executions>
					<execution>
						<id>generate-fmpp</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<config>${project.build.directory}/codegen/config.fmpp</config>
							<output>${project.build.directory}/generated-sources</output>
							<templates>${project.build.directory}/codegen/templates</templates>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<!-- This must be run AFTER the drill-fmpp-maven-plugin -->
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>javacc-maven-plugin</artifactId>
				<version>2.4</version>
				<executions>
					<execution>
						<phase>generate-sources</phase>
						<id>javacc</id>
						<goals>
							<goal>javacc</goal>
						</goals>
						<configuration>
							<sourceDirectory>${project.build.directory}/generated-sources/</sourceDirectory>
							<includes>
								<include>**/Parser.jj</include>
							</includes>
							<!-- This must be kept synced with Apache Calcite. -->
							<lookAhead>1</lookAhead>
							<isStatic>false</isStatic>
							<outputDirectory>${project.build.directory}/generated-sources/</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
