<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one
  ~ or more contributor license agreements.  See the NOTICE file
  ~ distributed with this work for additional information
  ~ regarding copyright ownership.  The ASF licenses this file
  ~ to you under the Apache License, Version 2.0 (the
  ~ "License"); you may not use this file except in compliance
  ~ with the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>flink-table</artifactId>
		<groupId>org.apache.flink</groupId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-sql-jdbc-driver-bundle</artifactId>
	<name>Flink : Table : SQL Jdbc Driver Bundle</name>

	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-sql-jdbc-driver</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-sql-client</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-sql-gateway-api</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-sql-gateway</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-common</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-annotations</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- Used to create Executor in connection. TODO jdbc driver should get rid of flink-core which is a big module. -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>com.github.siom79.japicmp</groupId>
				<artifactId>japicmp-maven-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
			<!-- Build flink sql jdbc driver bundle jar -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes combine.children="append">
									<include>org.apache.flink:flink-sql-jdbc-driver</include>
									<include>org.apache.flink:flink-sql-client</include>
									<include>org.apache.flink:flink-sql-gateway-api</include>
									<include>org.apache.flink:flink-sql-gateway</include>
									<include>org.apache.flink:flink-table-common</include>
									<include>org.apache.flink:flink-annotations</include>
									<include>org.apache.flink:flink-core</include>
									<include>org.apache.flink:flink-core-api</include>
									<include>org.apache.flink:flink-runtime</include>
									<include>org.apache.flink:flink-clients</include>
									<include>org.apache.flink:flink-table-api-java</include>
									<include>org.apache.flink:flink-json</include>
									<include>org.apache.flink:flink-shaded-netty</include>
									<include>org.apache.flink:flink-shaded-jackson</include>
									<include>org.apache.flink:flink-shaded-guava</include>
								</includes>
							</artifactSet>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
