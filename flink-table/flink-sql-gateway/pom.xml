<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one
  ~ or more contributor license agreements.  See the NOTICE file
  ~ distributed with this work for additional information
  ~ regarding copyright ownership.  The ASF licenses this file
  ~ to you under the Apache License, Version 2.0 (the
  ~ "License"); you may not use this file except in compliance
  ~ with the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>flink-table</artifactId>
        <groupId>org.apache.flink</groupId>
        <version>2.2-SNAPSHOT</version>
    </parent>

    <artifactId>flink-sql-gateway</artifactId>
    <name>Flink : Table : SQL Gateway</name>

    <packaging>jar</packaging>

	<properties>
		<surefire.module.config><!--
			SqlGatewayTest/CommonTestUtils.setEnv
			-->--add-opens=java.base/java.util=ALL-UNNAMED <!--
			OperationManager#getThreadInFuture
			-->--add-opens=java.base/java.util.concurrent=ALL-UNNAMED <!--
			SqlGatewayRestEndpointStatementITCase has to override AbstractSqlGatewayStatementITCase methods
			JUnit changed this behavior since 5.11.x, to restore it back need this flag as mentioned at
			https://junit.org/junit5/docs/current/user-guide/#extensions-supported-utilities-search-semantics
			-->-Djunit.platform.reflection.search.useLegacySemantics=true
		</surefire.module.config>
	</properties>

    <dependencies>
        <!-- Flink client to submit jobs -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-clients</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Table ecosystem -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-sql-gateway-api</artifactId>
            <version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-api-java-bridge</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>${quartz.version}</version>
            <optional>${flink.markBundledAsOptional}</optional>
        </dependency>

        <!-- Test dependencies -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-sql-gateway-api</artifactId>
            <version>${project.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-test-utils</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-planner_${scala.binary.version}</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-planner_${scala.binary.version}</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
            <type>test-jar</type>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-files</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-csv</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <scope>test</scope>
        </dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-jackson-module-jsonSchema</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-java</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-common</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-filesystem-test-utils</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
    <plugins>
        <!-- Build flink-sql-gateway jar -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-shade-plugin</artifactId>
            <executions>
                <!-- Exclude all flink-dist files and only contain sql-gateway files -->
                <execution>
                    <id>shade-flink</id>
                    <phase>package</phase>
                    <goals>
                        <goal>shade</goal>
                    </goals>
                    <configuration>
                        <artifactSet>
                            <includes combine.children="append">
                                <include>org.apache.flink:flink-sql-gateway-api</include>
                                <include>org.quartz-scheduler:quartz</include>
                            </includes>
                        </artifactSet>
                        <filters>
                            <filter>
                                <artifact>org.quartz-scheduler:quartz</artifact>
                                <excludes>
                                    <exclude>**/checkstyle.xml</exclude>
                                </excludes>
                            </filter>
                        </filters>
                        <relocations>
                            <relocation>
                                <pattern>org.quartz</pattern>
                                <shadedPattern>org.apache.flink.table.shaded.org.quartz</shadedPattern>
                            </relocation>
                            <relocation>
                                <pattern>org.terracotta.quartz</pattern>
                                <shadedPattern>org.apache.flink.table.shaded.org.terracotta.quartz</shadedPattern>
                            </relocation>
                        </relocations>
                    </configuration>
                </execution>
            </executions>
        </plugin>
        <!-- Make test classes available to other modules. -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
                <execution>
                    <goals>
                        <goal>test-jar</goal>
                    </goals>
					<configuration>
						<excludes>
							<!-- test-jar is still used by JUnit4 modules -->
							<exclude>META-INF/services/org.junit.jupiter.api.extension.Extension</exclude>
						</excludes>
					</configuration>
                </execution>
            </executions>
        </plugin>
    </plugins>
    </build>
</project>
