<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>flink-table</artifactId>
		<groupId>org.apache.flink</groupId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-table-calcite-bridge</artifactId>
	<name>Flink : Table : Calcite Bridge</name>
	<description>
		This module contains the Calcite dependencies for writing planner plugins (e.g. SQL dialects)
		that interact with Calcite APIs. More exactly, currently, it is intended to provide the ability
		to create RelNode, which involves accessing the RelOptCluster, RelBuilder, etc, provided by PlannerContext.
	</description>

	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<!-- Used for translation of table programs -->
			<groupId>org.apache.calcite</groupId>
			<artifactId>calcite-core</artifactId>
			<!-- When updating the Calcite version, make sure to update the dependency exclusions -->
			<version>${calcite.version}</version>
			<exclusions>
				<!--
				"mvn dependency:tree" as of Calcite 1.34.0:
				[INFO] +- org.apache.calcite:calcite-core:jar:1.34.0:compile
				[INFO] |  +- org.apache.calcite:calcite-linq4j:jar:1.34.0:compile
				[INFO] |  +- org.locationtech.jts:jts-core:jar:1.19.0:compile
				[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.15.3:compile
				[INFO] |  +- org.apache.calcite.avatica:avatica-core:jar:1.23.0:compile
				[INFO] |  +- org.apiguardian:apiguardian-api:jar:1.1.2:compile
				[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.15.3:compile
				[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.15.3:compile
				[INFO] |  +- com.jayway.jsonpath:json-path:jar:2.7.0:runtime
				[INFO] |  |  \- net.minidev:json-smart:jar:2.4.7:runtime
				[INFO] |  |     \- net.minidev:accessors-smart:jar:2.4.7:runtime
				[INFO] |  |        \- org.ow2.asm:asm:jar:9.1:runtime
				[INFO] |  +- commons-codec:commons-codec:jar:1.15:runtime
				[INFO] |  +- org.apache.commons:commons-math3:jar:3.6.1:runtime
				[INFO] |  \- commons-io:commons-io:jar:2.15.1:runtime

				Dependencies that are not needed for how we use Calcite right now.
				-->
				<exclusion>
					<groupId>org.apache.calcite.avatica</groupId>
					<artifactId>avatica-server</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.calcite.avatica</groupId>
					<artifactId>avatica-metrics</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.protobuf</groupId>
					<artifactId>protobuf-java</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.uzaygezen</groupId>
					<artifactId>uzaygezen-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.httpcomponents</groupId>
					<artifactId>httpclient</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.httpcomponents</groupId>
					<artifactId>httpcore</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-dbcp2</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-lang3</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.dataformat</groupId>
					<artifactId>jackson-dataformat-yaml</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.yahoo.datasketches</groupId>
					<artifactId>sketches-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>net.hydromatic</groupId>
					<artifactId>aggdesigner-algorithm</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.errorprone</groupId>
					<artifactId>error_prone_annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.jetbrains</groupId>
					<artifactId>annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.jetbrains.kotlin</groupId>
					<artifactId>kotlin-stdlib-jdk8</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.codehaus.janino</groupId>
					<artifactId>janino</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.codehaus.janino</groupId>
					<artifactId>commons-compiler</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.httpcomponents.client5</groupId>
					<artifactId>httpclient5</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.httpcomponents.core5</groupId>
					<artifactId>httpcore5</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.checkerframework</groupId>
					<artifactId>checker-qual</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.locationtech.jts.io</groupId>
					<artifactId>jts-io-common</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.locationtech.proj4j</groupId>
					<artifactId>proj4j</artifactId>
				</exclusion>
				<!--
				Exclude json-path as we are manually overriding it to a newer version (FLINK-36830).
				This can be removed once calcite is upgraded to 1.38 or greater, more details
				in JIRA issue FLINK-36602.
				-->
				<exclusion>
					<groupId>com.jayway.jsonpath</groupId>
					<artifactId>json-path</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--
		Override the json-path version used by Calcite to deal with CVE-2023-1370 (FLINK-36830).
		This can be removed once calcite is upgraded to 1.38 or greater, more details
		in JIRA issue FLINK-36602.
		 -->
		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.9.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-annotations</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-java</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>
</project>
