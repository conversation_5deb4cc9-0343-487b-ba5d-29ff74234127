<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one
  ~ or more contributor license agreements.  See the NOTICE file
  ~ distributed with this work for additional information
  ~ regarding copyright ownership.  The ASF licenses this file
  ~ to you under the Apache License, Version 2.0 (the
  ~ "License"); you may not use this file except in compliance
  ~ with the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-table</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-table-planner-loader</artifactId>
	<name>Flink : Table : Planner Loader</name>
	<packaging>jar</packaging>
	<description>
		This module contains the mechanism for loading flink-table-planner through a separate classloader.
		This allows arbitrary Scala versions in the classpath, hiding the Scala version used by the planner.
	</description>

	<dependencies>
		<dependency>
			<!-- StreamExecutorFactory -->
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-bridge-base</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<!-- Test utils -->
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>

		<dependency>
			<!-- Ensures that flink-table-planner is built beforehand, in order to bundle the jar -->
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-planner-loader-bundle</artifactId>
			<version>${project.version}</version>
			<!-- We don't want any production code from flink-table-planner-loader to reference flink-table-planner directly -->
			<scope>runtime</scope>
			<!-- Prevent dependency from being accessible to modules depending on flink-table-planner -->
			<optional>true</optional>
			<exclusions>
				<!-- Prevent pekko and scala from being visible to other modules depending on flink-table-planner-loader -->
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<classpathDependencyExcludes>
						<!-- The planner-loader and the planner_${scala.binary.version} jars are exclusive,
						that is you can have only one of them at the same time in the classpath,
						we need to exclude the planner_${scala.binary.version} from the test classpath
						in order to execute the loader tests correctly.
						-->
						<exclude>org.apache.flink:flink-table-planner-loader-bundle</exclude>
					</classpathDependencyExcludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-table-planner-jars</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy</goal>
						</goals>
						<configuration>
							<artifactItems>
								<artifactItem>
									<groupId>org.apache.flink</groupId>
									<artifactId>flink-table-planner-loader-bundle</artifactId>
									<version>${project.version}</version>
									<type>jar</type>
									<overWrite>true</overWrite>
									<destFileName>flink-table-planner.jar</destFileName>
								</artifactItem>
							</artifactItems>
							<outputDirectory>${project.build.directory}/classes</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<filters>
								<filter>
									<artifact>org.apache.flink:flink-table-planner-loader-bundle</artifact>
									<includes>
										<include>META-INF/NOTICE</include>
										<include>META-INF/licenses/**</include>
									</includes>
								</filter>
							</filters>
							<artifactSet>
								<includes>
									<include>org.apache.flink:flink-table-planner-loader-bundle</include>
								</includes>
							</artifactSet>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
