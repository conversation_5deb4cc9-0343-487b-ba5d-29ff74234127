<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-table</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-sql-client</artifactId>
	<name>Flink : Table : SQL Client</name>
	<description>
		This module contains the SQL Client for exploring and
		submitting SQL programs to Flink.
	</description>

	<packaging>jar</packaging>

	<properties>
		<surefire.module.config><!--
			Hadoop StringInternUtils
			-->--add-opens=java.base/java.net=ALL-UNNAMED <!--
			CommonTestUtils#setEnv
			-->--add-opens=java.base/java.util=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>

		<!-- Flink client to submit jobs -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- Table ecosystem -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-java-bridge</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- SQL Gateway -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-sql-gateway-api</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-sql-gateway</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- CLI for SQL CLI client -->
		<dependency>
			<groupId>org.jline</groupId>
			<artifactId>jline-terminal</artifactId>
			<version>3.21.0</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.jline</groupId>
			<artifactId>jline-reader</artifactId>
			<version>3.21.0</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- configuration -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-jackson</artifactId>
		</dependency>

		<!-- test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-java</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-csv</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-python</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-runtime</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-planner_${scala.binary.version}</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-files</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-java</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-planner_${scala.binary.version}</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-common</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-sql-gateway-api</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-sql-gateway</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<!-- Client parser -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-sql-parser</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
			<exclusions>
				<exclusion>
					<groupId>org.apache.calcite</groupId>
					<artifactId>calcite-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.calcite</groupId>
					<artifactId>calcite-testkit</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<!-- dependency convergence -->
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>1.9.4</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<!-- dependency convergence -->
				<groupId>org.codehaus.woodstox</groupId>
				<artifactId>stax2-api</artifactId>
				<version>4.2.1</version>
				<scope>test</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>

			<!-- Build flink-sql-client jar -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<!-- Exclude all flink-dist files -->
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes combine.children="append">
									<include>org.jline:*</include>
									<include>org.apache.flink:flink-sql-parser</include>
								</includes>
							</artifactSet>
							<!-- Keep it in sync with table-planner -->
							<relocations>
								<relocation>
									<pattern>com.google</pattern>
									<shadedPattern>org.apache.flink.calcite.shaded.com.google</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.commons.codec</pattern>
									<shadedPattern>org.apache.flink.calcite.shaded.org.apache.commons.codec</shadedPattern>
								</relocation>
								<!-- Currently Flink-SQL-Client depends on commons-io e.g.
								org.apache.flink.table.client.SqlClient#readFromURL
								<relocation>
									<pattern>org.apache.commons.io</pattern>
									<shadedPattern>org.apache.flink.calcite.shaded.org.apache.commons.io</shadedPattern>
								</relocation-->
								<relocation>
									<pattern>org.checkerframework</pattern>
									<shadedPattern>org.apache.flink.calcite.shaded.org.checkerframework</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
				<configuration>
					<filters>
						<filter>
							<!-- Only include TokenManager related class-->
							<artifact>org.apache.flink:flink-sql-parser</artifact>
							<includes>
								<include>org/apache/flink/sql/parser/impl/**</include>
							</includes>
						</filter>
					</filters>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
