<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-models</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-model-openai</artifactId>
	<name>Flink : Models : OpenAI</name>

	<properties>
		<openai.version>1.6.1</openai.version>
		<openai.kotlin.version>1.9.10</openai.kotlin.version>
		<okhttp.version>4.12.0</okhttp.version>
		<test.gson.version>2.11.0</test.gson.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.openai</groupId>
			<artifactId>openai-java</artifactId>
			<version>${openai.version}</version>
		</dependency>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>${okhttp.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-java</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-common</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-planner_${scala.binary.version}</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>mockwebserver</artifactId>
			<version>${okhttp.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-java-bridge</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.jetbrains.kotlin</groupId>
				<artifactId>kotlin-stdlib</artifactId>
				<version>${openai.kotlin.version}</version>
			</dependency>
			<dependency>
				<groupId>org.jetbrains.kotlin</groupId>
				<artifactId>kotlin-stdlib-jdk8</artifactId>
				<version>${openai.kotlin.version}</version>
			</dependency>
			<dependency>
				<groupId>org.jetbrains.kotlin</groupId>
				<artifactId>kotlin-stdlib-jdk7</artifactId>
				<version>${openai.kotlin.version}</version>
			</dependency>
			<dependency>
				<groupId>org.jetbrains.kotlin</groupId>
				<artifactId>kotlin-reflect</artifactId>
				<version>${openai.kotlin.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<repositories>
		<!-- openai-java is not yet available in redhat or google mirrors -->
		<repository>
			<id>forced-central</id>
			<url>https://repo1.maven.org/maven2</url>
		</repository>
	</repositories>
</project>
