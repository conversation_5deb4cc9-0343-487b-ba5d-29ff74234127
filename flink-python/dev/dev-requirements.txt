# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
pip>=20.3
setuptools>=75.3
wheel
apache-beam>=2.54.0,<=2.61.0
cython>=0.29.24
py4j==********
python-dateutil>=2.8.0,<3
cloudpickle~=2.2.0
avro>=1.12.0
pandas>=1.3.0
pyarrow>=5.0.0
pytz>=2018.3
numpy>=1.22.4,!=2.3.0
fastavro>=1.1.0,!=1.8.0
grpcio>=1.29.0,<=1.71.0
grpcio-tools>=1.29.0,<=1.71.0
pemja>=0.5.0,<0.6.0; platform_system != 'Windows'
httplib2>=0.19.0
protobuf~=4.25
pytest~=8.0
ruamel.yaml>=0.18.4
