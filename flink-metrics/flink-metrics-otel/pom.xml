<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-metrics</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-metrics-otel</artifactId>
	<name>Flink : Metrics : OpenTelemetry</name>

	<properties>
		<opentelemetry.version>1.30.1</opentelemetry.version>
		<opentelemetry.test.version>1.30.0</opentelemetry.test.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- OpenTelemetry -->

		<dependency>
			<groupId>io.opentelemetry</groupId>
			<artifactId>opentelemetry-sdk</artifactId>
			<version>${opentelemetry.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>io.opentelemetry</groupId>
			<artifactId>opentelemetry-sdk-common</artifactId>
			<version>${opentelemetry.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
			<exclusions>
				<exclusion>
					<groupId>com.google.protobuf</groupId>
					<artifactId>protobuf-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>io.opentelemetry</groupId>
			<artifactId>opentelemetry-exporter-logging</artifactId>
			<version>${opentelemetry.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>io.opentelemetry</groupId>
			<artifactId>opentelemetry-exporter-otlp</artifactId>
			<version>${opentelemetry.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>io.opentelemetry</groupId>
			<artifactId>opentelemetry-semconv</artifactId>
			<version>${opentelemetry.version}-alpha</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- test dependencies -->

		<dependency>
			<groupId>io.opentelemetry.javaagent</groupId>
			<artifactId>opentelemetry-testing-common</artifactId>
			<version>${opentelemetry.test.version}-alpha</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>io.opentelemetry.javaagent</groupId>
			<artifactId>opentelemetry-agent-for-testing</artifactId>
			<version>${opentelemetry.test.version}-alpha</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.mock-server</groupId>
			<artifactId>mockserver-netty-no-dependencies</artifactId>
			<version>5.15.0</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-jackson</artifactId>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes>
									<include>io.opentelemetry:*</include>
									<include>com.squareup.okhttp3:*</include>
									<include>com.squareup.okio:*</include>
								</includes>
							</artifactSet>
							<relocations>
								<relocation>
									<pattern>okhttp3/internal/publicsuffix</pattern>
									<shadedPattern>META-INF</shadedPattern>
									<includes>
										<include>**/NOTICE</include>
									</includes>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
