<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-metrics</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-metrics-influxdb</artifactId>
	<name>Flink : Metrics : InfluxDB</name>

	<dependencies>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.influxdb</groupId>
			<artifactId>influxdb-java</artifactId>
			<version>2.17</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<!-- test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-metrics-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>

		<dependency>
			<groupId>com.github.tomakehurst</groupId>
			<artifactId>wiremock-jre8</artifactId>
			<version>2.32.0</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes>
									<include>org.influxdb:*</include>
									<include>com.squareup.moshi:*</include>
									<include>com.squareup.okhttp3:*</include>
									<include>com.squareup.okio:*</include>
									<include>com.squareup.retrofit2:*</include>
									<!-- org.msgpack:msgpack-core is not required -->
								</includes>
							</artifactSet>
							<filters>
								<filter>
									<artifact>*</artifact>
									<excludes>
										<exclude>okhttp3/internal/publicsuffix/NOTICE</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
