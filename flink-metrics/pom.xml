<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-metrics</artifactId>
	<name>Flink : Metrics : </name>
	<packaging>pom</packaging>

	<properties>
		<dropwizard.version>3.2.6</dropwizard.version>
	</properties>

	<modules>
		<module>flink-metrics-core</module>
		<module>flink-metrics-dropwizard</module>
		<module>flink-metrics-graphite</module>
		<module>flink-metrics-influxdb</module>
		<module>flink-metrics-jmx</module>
		<module>flink-metrics-prometheus</module>
		<module>flink-metrics-statsd</module>
		<module>flink-metrics-datadog</module>
		<module>flink-metrics-slf4j</module>
		<module>flink-metrics-otel</module>
	</modules>

	<!-- override these root dependencies as 'provided', so they don't end up
		in the jars-with-dependencies. They are already contained
		in the flink-dist build -->

	<dependencies>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.google.code.findbugs</groupId>
			<artifactId>jsr305</artifactId>
			<scope>provided</scope>
		</dependency>
	</dependencies>

</project>
