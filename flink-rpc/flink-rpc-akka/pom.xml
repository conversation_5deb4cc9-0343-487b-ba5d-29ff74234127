<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-rpc</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-rpc-akka</artifactId>
	<name>Flink : RPC : Akka</name>
	<packaging>jar</packaging>
	<description>
		Pekko-based RPC implementation. Pekko is the Apache fork of Akka.
		For compatibility/git reasons not all mentions of Akka have been replaced.
	</description>

	<properties>
		<pekko.version>1.1.2</pekko.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-rpc-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<!-- used by the CustomSSLEngineProvider -->
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-netty</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.pekko</groupId>
			<artifactId>pekko-actor_${scala.binary.version}</artifactId>
			<version>${pekko.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.pekko</groupId>
			<artifactId>pekko-remote_${scala.binary.version}</artifactId>
			<version>${pekko.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
			<exclusions>
				<exclusion>
					<!-- optional dependency for UDP transport which we don't need -->
					<groupId>io.aeron</groupId>
					<artifactId>aeron-driver</artifactId>
				</exclusion>
				<exclusion>
					<!-- optional dependency for UDP transport which we don't need -->
					<groupId>io.aeron</groupId>
					<artifactId>aeron-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.pekko</groupId>
			<artifactId>pekko-slf4j_${scala.binary.version}</artifactId>
			<version>${pekko.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-all</artifactId>
			<scope>test</scope>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.google.code.findbugs</groupId>
			<artifactId>jsr305</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<!-- Overwrite parent dependency management -->
				<groupId>org.scala-lang</groupId>
				<artifactId>scala-library</artifactId>
				<version>${scala.version}</version>
			</dependency>
			<!-- For dependency convergence in Akka 2.6.20 -->
			<dependency>
				<groupId>com.typesafe</groupId>
				<artifactId>config</artifactId>
				<version>1.4.2</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes>
									<include>*</include>
								</includes>
							</artifactSet>
							<relocations>
								<relocation>
									<pattern>io.netty</pattern>
									<shadedPattern>org.apache.flink.shaded.netty4.io.netty</shadedPattern>
								</relocation>
							</relocations>
							<filters>
								<filter>
									<artifact>io.netty:*</artifact>
									<excludes>
										<!-- Only some of these licenses actually apply to the JAR and have been manually
											 placed in this module's resources directory. -->
										<exclude>META-INF/license/**</exclude>
										<!-- Only parts of NOTICE file actually apply to the netty JAR and have been manually
											 copied into this modules's NOTICE file. -->
										<exclude>META-INF/NOTICE.txt</exclude>
									</excludes>
								</filter>
								<filter>
									<artifact>org.scala-lang:*</artifact>
									<excludes>
										<!-- For deduplication purposes. -->
										<exclude>LICENSE</exclude>
										<exclude>NOTICE</exclude>
									</excludes>
								</filter>
							</filters>
							<transformers>
								<transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
									<resource>reference.conf</resource>
								</transformer>
							</transformers>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<!-- disable check for 2.12 dependencies -->
					<execution>
						<id>enforce-versions</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<phase>none</phase>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
