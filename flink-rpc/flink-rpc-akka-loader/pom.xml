<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-rpc</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-rpc-akka-loader</artifactId>
	<name>Flink : RPC : Akka-Loader</name>
	<packaging>jar</packaging>
	<description>This module contains the mechanism for loading flink-rpc-akka through a separate classloader.</description>

	<dependencies>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-rpc-core</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<!-- Ensures that flink-rpc-akka is built beforehand, in order to bundle the jar -->
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-rpc-akka</artifactId>
			<version>${project.version}</version>
			<!-- We don't want any production code from flink-rpc-akka-loader to reference flink-rpc-akka directly -->
			<scope>runtime</scope>
			<!-- Prevent dependency from being accessible to modules depending on flink-akka-loader-->
			<optional>true</optional>
			<exclusions>
				<!-- Prevent pekko and scala from being visible to other modules depending on flink-rpc-akka-loader -->
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
						<configuration>
							<excludes>
								<!-- test-jar is still used by JUnit4 modules -->
								<exclude>META-INF/services/org.junit.jupiter.api.extension.Extension</exclude>
							</excludes>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-rpc-akka-jars</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy</goal>
						</goals>
						<configuration>
							<artifactItems>
								<artifactItem>
									<groupId>org.apache.flink</groupId>
									<artifactId>flink-rpc-akka</artifactId>
									<version>${project.version}</version>
									<type>jar</type>
									<overWrite>true</overWrite>
									<destFileName>flink-rpc-akka.jar</destFileName>
								</artifactItem>
							</artifactItems>
							<outputDirectory>${project.build.directory}/classes</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
				<execution>
					<id>shade-flink</id>
					<phase>package</phase>
					<goals>
						<goal>shade</goal>
					</goals>
					<configuration>
						<artifactSet>
							<includes>
								<include>org.apache.flink:flink-rpc-akka</include>
							</includes>
						</artifactSet>
						<filters>
							<filter>
								<artifact>org.apache.flink:flink-rpc-akka</artifact>
								<includes>
									<include>META-INF/NOTICE</include>
									<include>META-INF/licenses/**</include>
								</includes>
							</filter>
						</filters>
					</configuration>
				</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>forbid-direct-akka-rpc-dependencies</id>
						<!-- This module needs the dependency in order to bundle flink-rpc-akka. -->
						<phase>none</phase>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
