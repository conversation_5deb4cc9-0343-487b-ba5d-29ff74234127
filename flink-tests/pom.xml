<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-tests</artifactId>
	<name>Flink : Tests</name>

	<packaging>jar</packaging>

	<properties>
		<surefire.module.config><!--
			chill ArraysAsListSerializer
			-->--add-opens=java.base/java.util=ALL-UNNAMED <!--
			Kryo File (GroupReduceITCase)
			-->--add-opens=java.base/java.io=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-files</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-files</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-datagen</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-guava</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-jackson</artifactId>
			<scope>test</scope>
		</dependency>
		
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-java</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>
		
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-examples-streaming</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<exclusions>
				<!-- Exclude the `flink-connector-kafka` dependency;
				otherwise, the `org.apache.flink.test.completeness.TypeSerializerTestCoverageTest`
				will fail because it checks the subclass of `TypeSerializer` within the
				`flink-connector-kafka` module. The failure message will be: "Could not find test
				'org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer$NextTransactionalIdHintSerializerTest'
				that covers 'org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer$NextTransactionalIdHintSerializer'." -->
				<exclusion>
					<groupId>org.apache.flink</groupId>
					<artifactId>flink-connector-kafka</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-hadoop-compatibility</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-hadoop-compatibility</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-test</artifactId>
			<version>${curator.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-guava</artifactId>
		</dependency>

		<dependency>
			<groupId>org.scalatest</groupId>
			<artifactId>scalatest_${scala.binary.version}</artifactId>
			<scope>test</scope>
		</dependency>
		
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.joda</groupId>
			<artifactId>joda-convert</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-statebackend-rocksdb</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>


		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-statebackend-rocksdb</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-dstl-dfs</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
        </dependency>

        <dependency>
			<groupId>com.github.oshi</groupId>
			<artifactId>oshi-core</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-avro</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<!-- For UpsertTestDynamicTableSinkITCase -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-json</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-migration-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<!-- utility to scan classpaths -->
		<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>dependency-convergence</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<skip>true</skip>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>com.diffplug.spotless</groupId>
				<artifactId>spotless-maven-plugin</artifactId>
				<configuration>
					<scala>
						<scalafmt>
							<version>${spotless.scalafmt.version}</version>
							<file>${project.basedir}/../.scalafmt.conf</file>
						</scalafmt>
						<licenseHeader>
							<content>${spotless.license.header}</content>
							<delimiter>${spotless.delimiter}</delimiter>
						</licenseHeader>
					</scala>
				</configuration>
			</plugin>

			<!-- Scala Compiler -->
			<plugin>
				<groupId>net.alchim31.maven</groupId>
				<artifactId>scala-maven-plugin</artifactId>
				<executions>
					<!-- Run scala compiler in the process-resources phase, so that dependencies on
						scala classes can be resolved later in the (Java) compile phase -->
					<execution>
						<id>scala-compile-first</id>
						<phase>process-resources</phase>
						<goals>
							<goal>compile</goal>
						</goals>
					</execution>
 
					<!-- Run scala compiler in the process-test-resources phase, so that dependencies on
						 scala classes can be resolved later in the (Java) test-compile phase -->
					<execution>
						<id>scala-test-compile</id>
						<phase>process-test-resources</phase>
						<goals>
							<goal>testCompile</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<jvmArgs>
						<jvmArg>-Xms128m</jvmArg>
						<jvmArg>-Xmx512m</jvmArg>
					</jvmArgs>
				</configuration>
			</plugin>
			
			<!-- Eclipse Integration -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-eclipse-plugin</artifactId>
				<version>2.8</version>
				<configuration>
					<downloadSources>true</downloadSources>
					<projectnatures>
						<projectnature>org.scala-ide.sdt.core.scalanature</projectnature>
						<projectnature>org.eclipse.jdt.core.javanature</projectnature>
					</projectnatures>
					<buildcommands>
						<buildcommand>org.scala-ide.sdt.core.scalabuilder</buildcommand>
					</buildcommands>
					<classpathContainers>
						<classpathContainer>org.scala-ide.sdt.launching.SCALA_CONTAINER</classpathContainer>
						<classpathContainer>org.eclipse.jdt.launching.JRE_CONTAINER</classpathContainer>
					</classpathContainers>
					<excludes>
						<exclude>org.scala-lang:scala-library</exclude>
						<exclude>org.scala-lang:scala-compiler</exclude>
					</excludes>
					<sourceIncludes>
						<sourceInclude>**/*.scala</sourceInclude>
						<sourceInclude>**/*.java</sourceInclude>
					</sourceIncludes>
				</configuration>
			</plugin>

			<!-- Adding scala source directories to build path
			 	 This is required for the source jar -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<executions>
					<!-- Add src/main/scala to build path -->
					<execution>
						<id>add-source</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>add-source</goal>
						</goals>
						<configuration>
							<sources>
								<source>src/main/scala</source>
							</sources>
						</configuration>
					</execution>
					<!-- Add src/test/scala to build path -->
					<execution>
						<id>add-test-source</id>
						<phase>generate-test-sources</phase>
						<goals>
							<goal>add-test-source</goal>
						</goals>
						<configuration>
							<sources>
								<source>src/test/scala</source>
							</sources>
						</configuration>
					</execution>
				</executions>
			</plugin>
		
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<systemPropertyVariables>
						<log.level>WARN</log.level>
					</systemPropertyVariables>
					<excludes>
						<exclude>**/*TestBase*.class</exclude>
					</excludes>
					<classpathDependencyExcludes>
						<classpathDependencyExclude>org.apache.curator:curator-recipes</classpathDependencyExclude>
						<classpathDependencyExclude>org.apache.curator:curator-client</classpathDependencyExclude>
						<classpathDependencyExclude>org.apache.curator:curator-framework</classpathDependencyExclude>
					</classpathDependencyExcludes>
					<environmentVariables>
						<!-- Make sure external hadoop environment will not affect maven building -->
						<HADOOP_HOME />
						<HADOOP_CONF_DIR />
					</environmentVariables>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.4</version><!--$NO-MVN-MAN-VER$-->
				<executions>

					<execution>
						<id>create-plugin-a-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>plugin-a</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-plugin-a-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>

					<execution>
						<id>create-plugin-b-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>plugin-b</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-plugin-b-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>

					<execution>
						<id>create-usercodetype-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.test.classloading.jar.UserCodeType</mainClass>
								</manifest>
							</archive>
							<finalName>usercodetype</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-usercodetype-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>create-custominputsplit-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.test.classloading.jar.CustomInputSplitProgram</mainClass>
								</manifest>
							</archive>
							<finalName>customsplit</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-custominput-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>create-streaming-custominputsplit-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.test.classloading.jar.StreamingCustomInputSplitProgram</mainClass>
								</manifest>
							</archive>
							<finalName>streaming-customsplit</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-streaming-custominput-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>create-streamingclassloader-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.test.classloading.jar.StreamingProgram</mainClass>
								</manifest>
							</archive>
							<finalName>streamingclassloader</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-streamingclassloader-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>create-streaming-state-checkpointed-classloader-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.test.classloading.jar.CheckpointedStreamingProgram</mainClass>
								</manifest>
							</archive>
							<finalName>streaming-checkpointed-classloader</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-streaming-state-checkpointed-classloader-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>create-custom_kv_state-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.test.classloading.jar.CustomKvStateProgram</mainClass>
								</manifest>
							</archive>
							<finalName>custom_kv_state</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-custom_kv_state-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>create-checkpointing_custom_kv_state-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.test.classloading.jar.CheckpointingCustomKvStateProgram</mainClass>
								</manifest>
							</archive>
							<finalName>checkpointing_custom_kv_state</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-checkpointing-custom_kv_state-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>create-classloading_policy-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.test.classloading.jar.ClassLoadingPolicyProgram</mainClass>
								</manifest>
							</archive>
							<finalName>classloading_policy</finalName>
							<attach>false</attach>
							<descriptors>
								<descriptor>src/test/assembly/test-classloading_policy-assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!--Remove the KMeansForTest code from the test-classes directory since it musn't be in the
			classpath when running the tests to actually test whether the user code class loader
			is properly used.-->
			<plugin>
				<artifactId>maven-clean-plugin</artifactId>
				<version>2.5</version><!--$NO-MVN-MAN-VER$-->
				<executions>
					<execution>
						<id>remove-classloading-test-dependencies</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>clean</goal>
						</goals>
						<configuration>
							<excludeDefaultDirectories>true</excludeDefaultDirectories>
							<filesets>
								<fileset>
									<directory>${project.build.testOutputDirectory}</directory>
									<includes>
										<include>**/classloading/jar/*.class</include>
									</includes>
								</fileset>
							</filesets>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.avro</groupId>
				<artifactId>avro-maven-plugin</artifactId>
				<version>${avro.version}</version>
				<executions>
					<execution>
						<id>generate-avro-test-sources</id>
						<phase>generate-test-sources</phase>
						<goals>
							<goal>schema</goal>
						</goals>
						<configuration>
							<testSourceDirectory>${project.basedir}/src/test/resources/avro</testSourceDirectory>
							<testOutputDirectory>${project.basedir}/target/generated-test-sources/avro</testOutputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>add-avro-test-source</id>
						<phase>generate-test-sources</phase>
						<goals>
							<goal>add-test-source</goal>
						</goals>
						<configuration>
							<sources>
								<source>${project.build.directory}/generated-test-sources/avro</source>
							</sources>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

    <profiles>
        <profile>
            <id>generate-migration-test-data</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>generate-migration-test-data</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
										<condition property="optional.classes" value="--classes '${generate.classes}'"
												   else="">
											<isset property="generate.classes"/>
										</condition>
										<condition property="optional.prefixes"
												   value="--prefixes '${generate.prefixes}'" else="">
											<isset property="generate.prefixes"/>
										</condition>
                                        <java classname="org.apache.flink.test.migration.MigrationTestsSnapshotGenerator"
                                              fork="true" failonerror="true" dir="${project.basedir}">
                                            <classpath refid="maven.test.classpath"/>
                                            <arg value="--dir"/>
											<arg line="${project.basedir}"/>
											<arg value="--version"/>
                                            <arg value="${generate.version}"/>
											<arg line="${optional.classes}"/>
											<arg line="${optional.prefixes}"/>
                                        </java>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
