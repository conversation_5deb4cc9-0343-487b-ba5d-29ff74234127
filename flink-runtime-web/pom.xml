<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-runtime-web</artifactId>
	<name>Flink : Runtime web</name>

	<packaging>jar</packaging>

	<properties>
		<test.parameterProgram.name>parameter-program</test.parameterProgram.name>
		<test.ParameterProgramNoManifest.name>parameter-program-without-manifest</test.ParameterProgramNoManifest.name>
		<test.ParameterProgramWithEagerSink.name>parameter-program-with-eager-sink</test.ParameterProgramWithEagerSink.name>
		<surefire.module.config><!--
			DataStream V2 use reflection to register sink transformation translator,
			it's a final map, we need to set this to pass tests in jdk17 or higher.
			-->--add-opens=java.base/java.util=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>

		<!-- ===================================================
						Flink Dependencies
			=================================================== -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- ===================================================
						Dependencies for the Web Server
			=================================================== -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-netty</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-guava</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-jackson</artifactId>
		</dependency>

		<!-- ===================================================
								Testing
			=================================================== -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-jackson-module-jsonSchema</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<profiles>
		<profile>
			<id>skip-webui-build</id>
			<build>
				<pluginManagement>
					<plugins>
						<plugin>
							<groupId>com.github.eirslett</groupId>
							<artifactId>frontend-maven-plugin</artifactId>
							<configuration>
								<skip>true</skip>
							</configuration>
						</plugin>
					</plugins>
				</pluginManagement>
			</build>
		</profile>
		<profile>
			<id>use-alibaba-mirror</id>
			<activation>
				<property>
					<name>use-alibaba-mirror</name>
				</property>
			</activation>
			<properties>
				<npm.proxy>--registry http://**********:8888/repository/npm/</npm.proxy>
			</properties>
		</profile>
	</profiles>

	<build>
		<resources>
			<resource>
				<!-- Only include the web folder from the web-dashboard directory -->
				<directory>web-dashboard</directory>
				<includes>
					<include>web/**</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
			</resource>
		</resources>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<!-- Used for JarHandler tests -->
						<id>test-program-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<includes>
								<include>org/apache/flink/runtime/webmonitor/handlers/utils/TestProgram.java</include>
							</includes>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.runtime.webmonitor.handlers.utils.TestProgram</mainClass>
								</manifest>
							</archive>
							<finalName>test-program</finalName>
						</configuration>
					</execution>
					<execution>
						<!-- Used for JarHandler tests -->
						<id>test-parameter-program-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<includes>
								<include>org/apache/flink/runtime/webmonitor/handlers/utils/TestProgram.java</include>
							</includes>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.runtime.webmonitor.testutils.ParameterProgram</mainClass>
								</manifest>
							</archive>
							<finalName>${test.parameterProgram.name}</finalName>
						</configuration>
					</execution>
					<execution>
						<!-- Used for JarHandler tests -->
						<id>test-parameter-program-jar-with-eager-sink</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<includes>
								<include>org/apache/flink/runtime/webmonitor/handlers/utils/EagerSinkProgram.java</include>
							</includes>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.runtime.webmonitor.handlers.utils.EagerSinkProgram</mainClass>
								</manifest>
							</archive>
							<finalName>${test.ParameterProgramWithEagerSink.name}</finalName>
						</configuration>
					</execution>
					<execution>
						<!-- Used for JarHandler tests -->
						<id>test-parameter-program-jar-without-manifest</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<includes>
								<include>org/apache/flink/runtime/webmonitor/handlers/utils/TestProgram.java</include>
							</includes>
							<finalName>${test.ParameterProgramNoManifest.name}</finalName>
						</configuration>
					</execution>
					<execution>
						<!-- Used for JarHandler tests -->
						<id>test-output-program-jar</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<includes>
								<include>org/apache/flink/runtime/webmonitor/handlers/utils/OutputTestProgram.java</include>
							</includes>
							<archive>
								<manifest>
									<mainClass>org.apache.flink.runtime.webmonitor.handlers.utils.OutputTestProgram</mainClass>
								</manifest>
							</archive>
							<finalName>output-test-program</finalName>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>com.github.eirslett</groupId>
				<artifactId>frontend-maven-plugin</artifactId>
				<version>1.15.1</version>
				<executions>
					<execution>
						<id>install node and npm</id>
						<goals>
							<goal>install-node-and-npm</goal>
						</goals>
						<configuration>
							<nodeVersion>v22.11.0</nodeVersion>
							<npmVersion>10.9.0</npmVersion>
						</configuration>
					</execution>
					<execution>
						<id>npm install</id>
						<goals>
							<goal>npm</goal>
						</goals>
						<configuration>
							<arguments>ci --cache-max=0 --no-save ${npm.proxy}</arguments>
							<environmentVariables>
								<HUSKY_SKIP_INSTALL>true</HUSKY_SKIP_INSTALL>
							</environmentVariables>
						</configuration>
					</execution>
					<execution>
						<id>npm run ci-check</id>
						<goals>
							<goal>npm</goal>
						</goals>
						<configuration>
							<arguments>run ci-check</arguments>
						</configuration>
					</execution>
				</executions>
				<configuration>
					<workingDirectory>web-dashboard</workingDirectory>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<systemPropertyVariables>
						<targetDir>${project.build.directory}</targetDir>
						<parameterJarName>${test.parameterProgram.name}</parameterJarName>
						<parameterJarWithoutManifestName>${test.ParameterProgramNoManifest.name}</parameterJarWithoutManifestName>
						<parameterJarWithEagerSinkName>${test.ParameterProgramWithEagerSink.name}</parameterJarWithEagerSinkName>
					</systemPropertyVariables>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
