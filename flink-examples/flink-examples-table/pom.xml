<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-examples</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<name>Flink : Examples : Table</name>
	<artifactId>flink-examples-table_${scala.binary.version}</artifactId>
	<packaging>jar</packaging>

	<properties>
		<surefire.module.config><!--
			chill ArraysAsListSerializer
			-->--add-opens=java.base/java.util=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>
		<!-- Flink core -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- Table ecosystem -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-api-java-bridge</artifactId>
			<version>${project.version}</version>
		</dependency>
		<!-- The following two dependencies are not required to define a SQL job pipeline,
		but only to execute it.

		In particular, here we're forced to use flink-table-planner_${scala.binary.version} instead of
		flink-table-planner-loader, because otherwise we hit this bug https://youtrack.jetbrains.com/issue/IDEA-93855
		when trying to run the examples from within IntelliJ IDEA. This is only relevant to this specific
		examples project, as it's in the same build tree of flink-parent.

		In a real environment, you need flink-table-runtime and flink-table-planner-loader either
		at test scope, for executing tests, or at provided scope, to run the main directly.
		 -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-runtime</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-table-planner_${scala.binary.version}</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- Table connectors and formats -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-files</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-csv</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- Test dependencies -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>com.diffplug.spotless</groupId>
				<artifactId>spotless-maven-plugin</artifactId>
				<configuration>
					<scala>
						<scalafmt>
							<version>${spotless.scalafmt.version}</version>
							<file>${project.basedir}/../../.scalafmt.conf</file>
						</scalafmt>
						<licenseHeader>
							<content>${spotless.license.header}</content>
							<delimiter>${spotless.delimiter}</delimiter>
						</licenseHeader>
					</scala>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>forbid-direct-table-planner-dependencies</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<!-- See comment above on table planner dependency -->
							<skip>true</skip>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- Fail compilation on deprecation warnings to prevent from showing users outdated examples. -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<executions>
					<execution>
						<id>compile</id>
						<phase>process-sources</phase>
						<goals>
							<goal>compile</goal>
						</goals>
						<configuration>
							<compilerArgument>-Xlint:deprecation</compilerArgument>
							<failOnWarning>true</failOnWarning>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>net.alchim31.maven</groupId>
				<artifactId>scala-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>scala-compile-first</id>
						<phase>process-resources</phase>
						<goals>
							<goal>add-source</goal>
							<goal>compile</goal>
						</goals>
						<configuration>
							<args>
								<arg>-deprecation</arg>
								<arg>-Xfatal-warnings</arg>
							</args>
						</configuration>
					</execution>
					<execution>
						<id>scala-test-compile</id>
						<phase>process-test-resources</phase>
						<goals>
							<goal>testCompile</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>

					<execution>
						<id>GettingStartedExample</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>

						<configuration>
							<classifier>GettingStartedExample</classifier>

							<archive>
								<manifestEntries>
									<program-class>org.apache.flink.table.examples.java.basics.GettingStartedExample</program-class>
								</manifestEntries>
							</archive>

							<includes>
								<include>org/apache/flink/table/examples/java/basics/GettingStartedExample*</include>
								<include>META-INF/LICENSE</include>
								<include>META-INF/NOTICE</include>
							</includes>
						</configuration>
					</execution>

					<execution>
						<id>UpdatingTopCityExample</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>

						<configuration>
							<classifier>UpdatingTopCityExample</classifier>

							<archive>
								<manifestEntries>
									<program-class>org.apache.flink.table.examples.java.basics.UpdatingTopCityExample</program-class>
								</manifestEntries>
							</archive>

							<includes>
								<include>org/apache/flink/table/examples/java/basics/UpdatingTopCityExample*</include>
								<include>META-INF/LICENSE</include>
								<include>META-INF/NOTICE</include>
							</includes>
						</configuration>
					</execution>

					<execution>
						<id>StreamSQLExample</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>

						<configuration>
							<classifier>StreamSQLExample</classifier>

							<archive>
								<manifestEntries>
									<program-class>org.apache.flink.table.examples.java.basics.StreamSQLExample</program-class>
								</manifestEntries>
							</archive>

							<includes>
								<include>org/apache/flink/table/examples/java/basics/StreamSQLExample*</include>
								<include>META-INF/LICENSE</include>
								<include>META-INF/NOTICE</include>
							</includes>
						</configuration>
					</execution>

					<execution>
						<id>StreamWindowSQLExample</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>

						<configuration>
							<classifier>StreamWindowSQLExample</classifier>

							<archive>
								<manifestEntries>
									<program-class>org.apache.flink.table.examples.java.basics.StreamWindowSQLExample</program-class>
								</manifestEntries>
							</archive>

							<includes>
								<include>org/apache/flink/table/examples/java/basics/StreamWindowSQLExample*</include>
								<include>META-INF/LICENSE</include>
								<include>META-INF/NOTICE</include>
							</includes>
						</configuration>
					</execution>

					<execution>
						<id>WordCountSQLExample</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>

						<configuration>
							<classifier>WordCountSQLExample</classifier>

							<archive>
								<manifestEntries>
									<program-class>org.apache.flink.table.examples.java.basics.WordCountSQLExample</program-class>
								</manifestEntries>
							</archive>

							<includes>
								<include>org/apache/flink/table/examples/java/basics/WordCountSQLExample*</include>
								<include>META-INF/LICENSE</include>
								<include>META-INF/NOTICE</include>
							</includes>
						</configuration>
					</execution>

					<execution>
						<id>ChangelogSocketExample</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>

						<configuration>
							<classifier>ChangelogSocketExample</classifier>

							<archive>
								<manifestEntries>
									<program-class>org.apache.flink.table.examples.java.connectors.ChangelogSocketExample</program-class>
								</manifestEntries>
							</archive>

							<includes>
								<include>org/apache/flink/table/examples/java/connectors/*</include>
								<include>**/META-INF/services/*</include>
								<include>META-INF/LICENSE</include>
								<include>META-INF/NOTICE</include>
							</includes>
						</configuration>
					</execution>

					<execution>
						<id>AdvancedFunctionsExample</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>

						<configuration>
							<classifier>AdvancedFunctionsExample</classifier>

							<archive>
								<manifestEntries>
									<program-class>org.apache.flink.table.examples.java.functions.AdvancedFunctionsExample</program-class>
								</manifestEntries>
							</archive>

							<includes>
								<include>org/apache/flink/table/examples/java/functions/*</include>
								<include>META-INF/LICENSE</include>
								<include>META-INF/NOTICE</include>
							</includes>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!--simplify the name of example JARs for build-target/examples -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>rename</id>
						<configuration>
							<target>
								<copy file="${project.basedir}/target/flink-examples-table_${scala.binary.version}-${project.version}-GettingStartedExample.jar" tofile="${project.basedir}/target/GettingStartedExample.jar"/>
								<copy file="${project.basedir}/target/flink-examples-table_${scala.binary.version}-${project.version}-UpdatingTopCityExample.jar" tofile="${project.basedir}/target/UpdatingTopCityExample.jar"/>
								<copy file="${project.basedir}/target/flink-examples-table_${scala.binary.version}-${project.version}-StreamSQLExample.jar" tofile="${project.basedir}/target/StreamSQLExample.jar"/>
								<copy file="${project.basedir}/target/flink-examples-table_${scala.binary.version}-${project.version}-StreamWindowSQLExample.jar" tofile="${project.basedir}/target/StreamWindowSQLExample.jar"/>
								<copy file="${project.basedir}/target/flink-examples-table_${scala.binary.version}-${project.version}-WordCountSQLExample.jar" tofile="${project.basedir}/target/WordCountSQLExample.jar"/>
								<copy file="${project.basedir}/target/flink-examples-table_${scala.binary.version}-${project.version}-ChangelogSocketExample.jar" tofile="${project.basedir}/target/ChangelogSocketExample.jar"/>
								<copy file="${project.basedir}/target/flink-examples-table_${scala.binary.version}-${project.version}-AdvancedFunctionsExample.jar" tofile="${project.basedir}/target/AdvancedFunctionsExample.jar"/>
							</target>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
