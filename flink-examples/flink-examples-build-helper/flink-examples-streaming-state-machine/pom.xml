<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>flink-examples-build-helper</artifactId>
		<groupId>org.apache.flink</groupId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-examples-streaming-state-machine</artifactId>
	<name>Flink : Examples : Build Helper : Streaming State machine</name>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-examples-streaming</artifactId>
			<version>${project.version}</version>
			<optional>${flink.markBundledAsOptional}</optional>
		</dependency>
	</dependencies>

	<build>
		<finalName>StateMachineExample</finalName>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<id>shade-flink</id>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<shadedArtifactAttached>false</shadedArtifactAttached>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<transformers>
								<transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
									<mainClass>org.apache.flink.streaming.examples.statemachine.StateMachineExample</mainClass>
								</transformer>
							</transformers>
							<artifactSet>
								<includes>
                                    <include>org.apache.flink:flink-connector-datagen</include>
                                    <include>org.apache.flink:flink-connector-kafka</include>
									<include>org.apache.flink:flink-examples-streaming</include>
									<include>org.apache.kafka:*</include>
								</includes>
							</artifactSet>

							<filters>
								<filter>
									<artifact>org.apache.flink:flink-examples-streaming</artifact>
									<includes>
										<include>org/apache/flink/streaming/examples/statemachine/**</include>
									</includes>
								</filter>
								<filter>
									<artifact>org.apache.kafka:*</artifact>
									<excludes>
										<exclude>LICENSE</exclude>
										<!-- Does not contain anything relevant.
                                            Cites a binary dependency on jersey, but this is neither reflected in the
                                            dependency graph, nor are any jersey files bundled. -->
										<exclude>NOTICE</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
