<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-yarn</artifactId>
	<name>Flink : Yarn</name>
	<packaging>jar</packaging>

	<properties>
		<!-- for testing (will override Hadoop's default dependency on too low SDK versions that
			do not work with our httpcomponents version) -->
		<aws.sdk.version>1.12.779</aws.sdk.version>
		<surefire.module.config><!--
			CommonTestUtils#setEnv
			-->--add-opens=java.base/java.util=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>

		<!-- core dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-clients</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-hdfs</artifactId>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-yarn-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-yarn-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-mapreduce-client-core</artifactId>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		

		<!-- test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-hdfs</artifactId>
			<scope>test</scope>
			<type>test-jar</type>
			<version>${flink.hadoop.version}</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<scope>test</scope>
			<type>test-jar</type>
			<version>${flink.hadoop.version}</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<!-- This dependency is no longer shipped with the JDK since Java 9.-->
					<groupId>jdk.tools</groupId>
					<artifactId>jdk.tools</artifactId>
				</exclusion>
					<exclusion>
						<groupId>ch.qos.reload4j</groupId>
						<artifactId>reload4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-reload4j</artifactId>
					</exclusion>
			</exclusions>
		</dependency>

		<!-- for the S3 tests of YarnFileStageTestS3ITCase -->
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-aws</artifactId>
			<version>${flink.hadoop.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.avro</groupId>
					<artifactId>avro</artifactId>
				</exclusion>
				<!-- The aws-java-sdk-core requires jackson 2.6, but
					hadoop pulls in 2.3 -->
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- override Hadoop's default dependency on too low SDK versions that do not work
			with our httpcomponents version when initialising the s3a file system -->
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-s3</artifactId>
			<version>${aws.sdk.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-sts</artifactId>
			<version>${aws.sdk.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<!-- dependency convergence -->
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<!-- Beanutils 1.9.+ doesn't work with Hadoop 2 -->
				<version>1.8.3</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<!-- dependency convergence -->
				<groupId>org.codehaus.woodstox</groupId>
				<artifactId>stax2-api</artifactId>
				<version>4.2.1</version>
				<scope>test</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<profiles>
		<profile>
			<!-- Hadoop >= 2.6 moved the S3 file systems from hadoop-common into hadoop-aws artifact
				(see https://issues.apache.org/jira/browse/HADOOP-11074)
				We can add the (test) dependency per default once 2.6 is the minimum required version.
			-->
			<id>include_hadoop_aws</id>
			<activation>
				<property>
					<name>include_hadoop_aws</name>
				</property>
			</activation>
			<dependencies>
				<!-- for the S3 tests of YarnFileStageTestS3ITCase -->
				<dependency>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-aws</artifactId>
					<version>${flink.hadoop.version}</version>
					<scope>test</scope>
					<exclusions>
						<exclusion>
							<groupId>log4j</groupId>
							<artifactId>log4j</artifactId>
						</exclusion>
						<exclusion>
							<groupId>org.slf4j</groupId>
							<artifactId>slf4j-log4j12</artifactId>
						</exclusion>
						<exclusion>
							<groupId>org.apache.avro</groupId>
							<artifactId>avro</artifactId>
						</exclusion>
						<!-- The aws-java-sdk-core requires jackson 2.6, but
							hadoop pulls in 2.3 -->
						<exclusion>
							<groupId>com.fasterxml.jackson.core</groupId>
							<artifactId>jackson-annotations</artifactId>
						</exclusion>
						<exclusion>
							<groupId>com.fasterxml.jackson.core</groupId>
							<artifactId>jackson-core</artifactId>
						</exclusion>
						<exclusion>
							<groupId>com.fasterxml.jackson.core</groupId>
							<artifactId>jackson-databind</artifactId>
						</exclusion>
						<exclusion>
							<groupId>ch.qos.reload4j</groupId>
							<artifactId>reload4j</artifactId>
						</exclusion>
						<exclusion>
							<groupId>org.slf4j</groupId>
							<artifactId>slf4j-reload4j</artifactId>
						</exclusion>
					</exclusions>
				</dependency>
				<!-- override Hadoop's default dependency on too low SDK versions that do not work
					with our httpcomponents version when initialising the s3a file system -->
				<dependency>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-s3</artifactId>
					<version>${aws.sdk.version}</version>
					<scope>test</scope>
				</dependency>
				<dependency>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-sts</artifactId>
					<version>${aws.sdk.version}</version>
					<scope>test</scope>
				</dependency>
			</dependencies>
		</profile>
	</profiles>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>dependency-convergence</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<skip>true</skip>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
