<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-streaming-java</artifactId>
	<name>Flink : Streaming Java</name>

	<packaging>jar</packaging>

	<properties>
		<!--
		Two properties below should be merged once support for jdk8 and jdk11 is dropped.
		The reason of extraction into a separate property with profile is that
		specifying -Djava.security.manager=allow within ${surefire.module.config} leads to
		hanging of surefire for jdk8 and jdk11
		-->
		<surefire.module.config.jdk21></surefire.module.config.jdk21>
		<surefire.module.config> ${surefire.module.config.jdk21} <!--
			FromElementsFunctionTest
			-->--add-opens=java.base/java.lang=ALL-UNNAMED <!--
			chill ArraysAsListSerializer
			-->--add-opens=java.base/java.util=ALL-UNNAMED
		</surefire.module.config>
	</properties>

	<dependencies>

		<!-- core dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-file-sink-common</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-shaded-guava</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-math3</artifactId>
			<version>3.6.1</version>
		</dependency>

		<!-- test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-migration-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>generate-migration-test-data</id>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-antrun-plugin</artifactId>
						<executions>
							<execution>
								<id>generate-migration-test-data</id>
								<phase>package</phase>
								<goals>
									<goal>run</goal>
								</goals>
								<configuration>
									<target>
										<condition property="optional.classes" value="--classes '${generate.classes}'"
												   else="">
											<isset property="generate.classes"/>
										</condition>
										<condition property="optional.prefixes"
												   value="--prefixes '${generate.prefixes}'" else="">
											<isset property="generate.prefixes"/>
										</condition>
										<java classname="org.apache.flink.test.migration.MigrationTestsSnapshotGenerator"
											  fork="true" failonerror="true" dir="${project.basedir}">
											<classpath refid="maven.test.classpath"/>
											<arg value="--dir"/>
											<arg line="${project.basedir}"/>
											<arg value="--version"/>
											<arg value="${generate.version}"/>
											<arg line="${optional.classes}"/>
											<arg line="${optional.prefixes}"/>
										</java>
									</target>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>java21</id>
			<activation>
				<jdk>[21,)</jdk>
			</activation>

			<properties>
				<surefire.module.config.jdk21> <!--
				    required by StreamTaskSystemExitTest-->
					-Djava.security.manager=allow</surefire.module.config.jdk21>
			</properties>
		</profile>
	</profiles>
</project>
