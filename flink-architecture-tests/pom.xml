<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<artifactId>flink-architecture-tests</artifactId>
	<name>Flink : Architecture Tests</name>
	<packaging>pom</packaging>

	<modules>
		<module>flink-architecture-tests-base</module>
		<module>flink-architecture-tests-production</module>
		<module>flink-architecture-tests-test</module>
	</modules>

	<dependencyManagement>

		<dependencies>
			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-architecture-tests-base</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-annotations</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<!-- Tested Flink modules -->

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-core</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<!-- Table API & SQL -->

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-table-common</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-table-api-java</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-table-api-java-bridge</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-table-code-splitter</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-table-runtime</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-table-planner_${scala.binary.version}</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-sql-client</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-sql-gateway-api</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-sql-gateway</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<!-- Connectors -->

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-connector-base</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-connector-files</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-connector-datagen</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<!-- Models -->

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-model-openai</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>

			<!-- Test Utils -->

			<dependency>
				<groupId>org.apache.flink</groupId>
				<artifactId>flink-test-utils</artifactId>
				<version>${project.version}</version>
				<scope>test</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>dependency-convergence</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<skip>true</skip>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
