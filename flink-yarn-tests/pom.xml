<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.apache.flink</groupId>
		<artifactId>flink-parent</artifactId>
		<version>2.2-SNAPSHOT</version>
	</parent>

	<!--
	There is a separate "flink-yarn-tests" package that expects the "flink-dist" package
	to be build before.
	We need the YARN fat jar build by flink-dist for the tests.
	-->
	
	<artifactId>flink-yarn-tests</artifactId>
	<name>Flink : Yarn Tests</name>
	<packaging>jar</packaging>

	<properties>
		<japicmp.skip>true</japicmp.skip>
		<surefire.module.config><!--
			CommonTestUtils#setEnv
			-->--add-opens=java.base/java.util=ALL-UNNAMED <!--
			MiniYARNCluster
			-->--add-opens=java.base/java.lang=ALL-UNNAMED <!--
			YARNSessionFIFOSecuredITCase
			-->--add-exports=java.security.jgss/sun.security.krb5=ALL-UNNAMED <!--
			YARNSessionCapacitySchedulerITCase has to override YarnTestBase methods
			JUnit changed this behavior since 5.11.x, to restore it back need this flag as mentioned at
			https://junit.org/junit5/docs/current/user-guide/#extensions-supported-utilities-search-semantics
			-->-Djunit.platform.reflection.search.useLegacySemantics=true
		</surefire.module.config>
	</properties>

	<dependencies>
		
		<!-- test dependencies -->

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils-junit</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-dist_${scala.binary.version}</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>pom</type>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-runtime</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>

		<!-- Needed for the streaming wordcount example -->
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-java</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-yarn</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-examples-streaming</artifactId>
			<version>${project.version}</version>
			<type>jar</type>
			<classifier>WordCount</classifier>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-test</artifactId>
			<version>${curator.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<!-- Use whatever guava version Hadoop pulls in. -->
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<version>${flink.hadoop.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<!-- This dependency is no longer shipped with the JDK since Java 9.-->
					<groupId>jdk.tools</groupId>
					<artifactId>jdk.tools</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-yarn-client</artifactId>
			<version>${flink.hadoop.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<!-- This dependency is no longer shipped with the JDK since Java 9.-->
					<groupId>jdk.tools</groupId>
					<artifactId>jdk.tools</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-yarn-api</artifactId>
			<version>${flink.hadoop.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<!-- This dependency is no longer shipped with the JDK since Java 9.-->
					<groupId>jdk.tools</groupId>
					<artifactId>jdk.tools</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-minicluster</artifactId>
			<version>${flink.hadoop.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-minikdc</artifactId>
			<version>${minikdc.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-reload4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.reload4j</groupId>
					<artifactId>reload4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>dependency-convergence</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<skip>true</skip>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<executions>
					<execution>
						<id>integration-tests</id>
						<configuration>
							<!-- Enforce single threaded execution due to port conflicts with the mini yarn cluster -->
							<forkCount>1</forkCount>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- Skip the deployment of this module since it only contains yarn tests -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<!--
			Copy batch and streaming examples programs in to the flink-yarn-tests/target/programs
			directory to be run during YARN integration tests.
			-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy</id>
						<phase>pre-integration-test</phase>
						<goals>
							<goal>copy</goal>
						</goals>
						<configuration>
							<artifactItems>
								<artifactItem>
									<groupId>org.apache.flink</groupId>
									<artifactId>flink-examples-streaming</artifactId>
									<type>jar</type>
									<classifier>WordCount</classifier>
									<overWrite>true</overWrite>
									<destFileName>StreamingWordCount.jar</destFileName>
								</artifactItem>
								<artifactItem>
									<groupId>org.apache.flink</groupId>
									<artifactId>flink-examples-streaming</artifactId>
									<type>jar</type>
									<classifier>WindowJoin</classifier>
									<overWrite>true</overWrite>
									<destFileName>WindowJoin.jar</destFileName>
								</artifactItem>
							</artifactItems>
							<outputDirectory>${project.build.directory}/programs</outputDirectory>
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>true</overWriteSnapshots>
						</configuration>
					</execution>
					<!-- Write classpath of flink-yarn-tests to a file, so that the yarn tests can use it as their classpath
						for the YARN "containers".
					-->
					<execution>
						<id>store-classpath-in-target-for-tests</id>
						<phase>process-test-resources</phase>
						<goals>
							<goal>build-classpath</goal>
						</goals>
						<configuration>
							<outputFile>${project.build.directory}/yarn.classpath</outputFile>
							<excludeGroupIds>org.apache.flink</excludeGroupIds>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
