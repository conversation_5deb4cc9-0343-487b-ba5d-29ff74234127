# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

baseURL = '//nightlies.apache.org/flink/flink-docs-master'
languageCode = "en-us"
title = "Apache Flink"
enableGitInfo = false
theme = "book"
pygmentsUseClasses = true

[params]
  # Flag whether this is a stable version or not.
  # Used for the quickstart page.
  IsStable = false

  # Flag to indicate whether an outdated warning should be shown.
  ShowOutDatedWarning = false

  # This are the version referenced in the docs. Please only use these variables
  # to reference a specific Flink version, because this is the only place where
  # we change the version for the complete docs when forking of a release branch
  # etc.
  # The full version string as referenced in Maven (e.g. 1.2.1)
  Version = "2.2-SNAPSHOT"

  # For stable releases, leave the bugfix version out (e.g. 1.2). For snapshot
  # release this should be the same as the regular version
  VersionTitle = "2.2-SNAPSHOT"

  # The branch for this version of Apache Flink
  Branch = "master"

  # The github repository for Apache Flink
  Repo = "//github.com/apache/flink"

  GithubRepo = "https://github.com/apache/flink.git"

  # Flink training exercises
  TrainingExercises = "//github.com/apache/flink-training"

  # This suffix is appended to the Scala-dependent Maven artifact names
  ScalaVersion = "_2.12"

  ProjectHomepage = "//flink.apache.org"

  DownloadPage = "//flink.apache.org/downloads.html"

  ZhDownloadPage = "//flink.apache.org/zh/downloads.html"

  JavaDocs = "//nightlies.apache.org/flink/flink-docs-master/api/java/"

  PyDocs = "//nightlies.apache.org/flink/flink-docs-master/api/python/"

  # External links at the bottom
  # of the menu
  MenuLinks = [
    ["Project Homepage", "//flink.apache.org"],
    ["JavaDocs", "//nightlies.apache.org/flink/flink-docs-master/api/java/"],
    ["PyDocs", "//nightlies.apache.org/flink/flink-docs-master/api/python/"]
  ]

  PreviousDocs = [
    ["2.0", "http://nightlies.apache.org/flink/flink-docs-release-2.0"],
    ["1.20", "http://nightlies.apache.org/flink/flink-docs-release-1.20"],
    ["1.19", "http://nightlies.apache.org/flink/flink-docs-release-1.19"],
    ["1.18", "http://nightlies.apache.org/flink/flink-docs-release-1.18"],
    ["1.17", "http://nightlies.apache.org/flink/flink-docs-release-1.17"],
    ["1.16", "http://nightlies.apache.org/flink/flink-docs-release-1.16"],
    ["1.15", "http://nightlies.apache.org/flink/flink-docs-release-1.15"],
    ["1.14", "http://nightlies.apache.org/flink/flink-docs-release-1.14"],
    ["1.13", "http://nightlies.apache.org/flink/flink-docs-release-1.13"],
    ["1.12", "http://nightlies.apache.org/flink/flink-docs-release-1.12"],
    ["1.11", "http://nightlies.apache.org/flink/flink-docs-release-1.11"],
    ["1.10", "http://nightlies.apache.org/flink/flink-docs-release-1.10"],
    ["1.9", "http://nightlies.apache.org/flink/flink-docs-release-1.9"],
    ["1.8", "http://nightlies.apache.org/flink/flink-docs-release-1.8"],
    ["1.7", "http://nightlies.apache.org/flink/flink-docs-release-1.7"],
    ["1.6", "http://nightlies.apache.org/flink/flink-docs-release-1.6"],
    ["1.5", "http://nightlies.apache.org/flink/flink-docs-release-1.5"],
    ["1.4", "http://nightlies.apache.org/flink/flink-docs-release-1.4"],
    ["1.3", "http://nightlies.apache.org/flink/flink-docs-release-1.3"],
    ["1.2", "http://nightlies.apache.org/flink/flink-docs-release-1.2"],
    ["1.1", "http://nightlies.apache.org/flink/flink-docs-release-1.1"],
    ["1.0", "http://nightlies.apache.org/flink/flink-docs-release-1.0"]
  ]

[markup]
[markup.goldmark.renderer]
  unsafe = true
[markup.tableOfContents]
  startLevel = 1

[languages]
[languages.en]
  languageName = 'English'
  contentDir = 'content'
  weight = 1

[languages.zh]
  languageName = '中文版'
  contentDir = 'content.zh'
  weight = 2

[module]
[[module.imports]]
  path = 'connectors'
[[module.imports.mounts]]
  source = 'content'
  target = 'content'
  lang = 'en'
[[module.imports.mounts]]
  source = 'content.zh'
  target = 'content'
  lang = 'zh'
[[module.imports.mounts]]
  source = 'layouts'
  target = 'layouts'
[[module.imports.mounts]]
  source = 'data'
  target = 'data'
